import type { Shortcuts } from '../types'

export default {
  // Popover animations
  'animate-popover': 'animate-in fade-in-0 zoom-in-95 data-[state=closed]:(animate-out fade-out-0 zoom-out-95)',
  'animate-popover-l': 'data-[side=left]:slide-in-from-right-2',
  'animate-popover-r': 'data-[side=right]:slide-in-from-left-2',
  'animate-popover-t': 'data-[side=top]:slide-in-from-bottom-2',
  'animate-popover-b': 'data-[side=bottom]:slide-in-from-top-2',

  // Collapse animations
  'animate-collapse': 'transition ease-in-out data-[state=open]:(animate-in animate-duration-500 slide-in-from-bottom) data-[state=closed]:(animate-out animate-duration-300 slide-out-to-bottom)',
} satisfies Shortcuts
