import type { Preflights } from '../types'
import * as themes from '../../'
import { minifyCss } from '../utils'

export function preflightThemes(): Preflights {
  return Object.values(themes)
    .map(theme => ({
      name: `theme-${theme.name}`,
      getCSS() {
        return minifyCss(`
/* ${theme.name} theme */
.${theme.name} {
  ${Object.entries(theme.colors).map(([key, value]) => `--${key}: ${value};`).join('')}
}
`)
      },
    }))
}
