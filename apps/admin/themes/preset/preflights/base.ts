import type { Preflights } from '../types'
import { minifyCss } from '../utils'

export function preflightBase(): Preflights {
  return [
    {
      getCSS() {
        return minifyCss(`
html {
  height: 100%;
  font-size: 16px;
  scroll-behavior: smooth;
  font-synthesis-weight: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  color: rgb(var(--text));
  background-color: rgb(var(--body));
}

:root {
  --primary: var(--primary-500);
  --gray: var(--gray-500);
  --un-default-border-color: rgb(var(--border));
}

::-webkit-scrollbar {
  width: 0.5rem;
}

::-webkit-scrollbar:horizontal {
  height: 0.5rem;
}

::-webkit-scrollbar-track {
  background: rgb(var(--body));
  border-radius: 6px;
}

::-webkit-scrollbar-thumb {
  background: rgb(var(--toned) / 0.5);
  border-radius: 6px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgb(var(--toned));
}

::-moz-selection {
  background: rgb(var(--primary) / 0.2);
}

::selection {
  background: rgb(var(--primary) / 0.2);
}`)
      },
    },
  ]
}
