import type { Theme } from '../types'

export const animation = {
  keyframes: {
    'skeleton': '{0% { transform: translate(-100%) skew(-15deg) } 100% { transform: translate(100%) skew(-15deg) }}',
    'accordion-up': '{0% { height: var(--reka-accordion-content-height) } 100% { height: 0 }}',
    'accordion-down': '{0% { height: 0 } 100% { height: var(--reka-accordion-content-height) }}',
    'collapse-up': '{0% { height: var(--reka-collapsible-content-height) } 100% { height: 0 }}',
    'collapse-down': '{0% { height: 0 } 100% { height: var(--reka-collapsible-content-height) }}',
    'toast-closed': '{0% { transform: var(--transform) } 100% { transform: translateY(calc((var(--offset) - var(--height)) * var(--translate-factor))) }}',
    'toast-collapsed': '{0% { transform: var(--transform) } 100% { transform: translateY(calc((var(--before) - var(--height)) * var(--gap))) scale(var(--scale)) }}',
    'toast-slide-left': '{0% { transform: translateX(0) translateY(var(--translate)) } 100% { transform: translateX(-100%) translateY(var(--translate)) }}',
    'toast-slide-right': '{0% { transform: translateX(0) translateY(var(--translate)) } 100% { transform: translateX(100%) translateY(var(--translate)) }}',
    'toast-slide-up': '{0% { transform: translateX(var(--translate)) translateY(0) } 100% { transform: translateX(var(--translate)) translateY(-100%) }}',
    'toast-slide-down': '{0% { transform: translateX(var(--translate)) translateY(0) } 100% { transform: translateX(var(--translate)) translateY(100%) }}',
  },
  durations: {
    'skeleton': '1.5s',
    'accordion-up': '0.2s',
    'accordion-down': '0.2s',
    'collapse-up': '0.2s',
    'collapse-down': '0.2s',
    'toast-closed': '0.2s',
    'toast-collapsed': '0.2s',
    'toast-slide-left': '0.2s',
    'toast-slide-right': '0.2s',
    'toast-slide-up': '0.2s',
    'toast-slide-down': '0.2s',
  },
  timingFns: {
    'skeleton': 'linear 2s',
    'accordion-up': 'ease-out',
    'accordion-down': 'ease-out',
    'collapse-up': 'ease-out',
    'collapse-down': 'ease-out',
    'toast-closed': 'ease-in-out',
    'toast-collapsed': 'ease-in-out',
    'toast-slide-left': 'ease-out',
    'toast-slide-right': 'ease-out',
    'toast-slide-up': 'ease-out',
    'toast-slide-down': 'ease-out',
  },
  counts: {
    skeleton: 'infinite',
  },
} satisfies Theme['animation']
