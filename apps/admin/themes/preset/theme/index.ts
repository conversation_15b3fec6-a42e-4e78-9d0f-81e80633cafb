import type { Theme } from '../types'
import { animation } from './animation'
import { colors } from './colors'
import { fontFamily, fontSize } from './font'
import { borderRadius, boxShadow, breakpoints, verticalBreakpoints, zIndex } from './misc'
import { height } from './size'

export const theme = {
  animation,
  colors,
  borderRadius,
  fontFamily,
  fontSize,
  breakpoints,
  boxShadow,
  verticalBreakpoints,
  height,
  zIndex,
} satisfies Theme
