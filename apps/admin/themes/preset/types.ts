import type {
  PresetUnoTheme as Theme,
  Preflight as UnoPreflight,
  Rule as UnoRule,
  Variant as UnoVariant,
  UserShortcuts,
} from 'unocss'

type Rule = UnoRule<Theme>
type Shortcuts = UserShortcuts<Theme>
type Variant = UnoVariant<Theme>
type Variants = Variant[]
type Preflight = UnoPreflight<Theme>
type Preflights = Preflight[]

export type { Preflight, Preflights, Rule, Shortcuts, Theme, Variants }
