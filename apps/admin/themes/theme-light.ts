import type { ThemeRegister } from '~/types'

export default {
  name: 'light',
  mode: 'light',
  background: 'translucent',
  colors: {
    tooltip: 'var(--gray-700)',
    popover: '255 255 255',
    panel: '255 255 255',
    body: 'var(--gray-50)',
    highlighted: 'var(--gray-950)',
    text: 'var(--gray-700)',
    toned: 'var(--gray-400)',
    muted: 'var(--gray-300)',
    dimmed: 'var(--gray-100)',
    border: 'var(--gray-200)',
  },
} satisfies ThemeRegister
