export function useCurrentWorkspaceId() {
  const route = useRoute()

  return computed(() => {
    if (route.params.workspace)
      return route.params.workspace as string

    return route.fullPath.split('/')[1]
  })
}

export async function useUserWorkspaces() {
  const { data, status } = await useAsyncData(
    'user:workspaces',
    () => $fetch('/api/session/workspaces'),
  )

  const active = computed(() => {
    const current = useCurrentWorkspaceId()

    return data.value?.find(
      w => w.id === current.value,
    )
  })

  const loading = computed(
    () => status.value === 'pending',
  )

  return {
    loading,
    active,
    workspaces: data,
  }
}
