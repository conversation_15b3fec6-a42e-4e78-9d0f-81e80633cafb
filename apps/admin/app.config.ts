import type { ThemeSettings } from '~/types'

export default defineAppConfig({
  theme: {
    name: 'light',
    background: 'translucent',
    primary: '#020617',
    gray: '#6b7280',
  } as ThemeSettings,
  preset: {
    primary: [
      { name: 'Black', value: '#020617' },
      { name: 'Red', value: '#ef4444' },
      { name: 'Orange', value: '#f97316' },
      { name: '<PERSON>', value: '#f59e0b' },
      { name: 'Yellow', value: '#eab308' },
      { name: '<PERSON><PERSON>', value: '#84cc16' },
      { name: 'Green', value: '#22c55e' },
      { name: '<PERSON>', value: '#10b981' },
      { name: '<PERSON><PERSON>', value: '#14b8a6' },
      { name: '<PERSON><PERSON>', value: '#06b6d4' },
      { name: '<PERSON>', value: '#0ea5e9' },
      { name: 'Blue', value: '#0055ff' },
      { name: 'Indigo', value: '#6366f1' },
      { name: '<PERSON>', value: '#8b5cf6' },
      { name: '<PERSON>', value: '#a855f7' },
      { name: '<PERSON><PERSON><PERSON>', value: '#d946ef' },
      { name: 'Pink', value: '#ec4899' },
      { name: 'Rose', value: '#f43f5e' },
    ],
    gray: [
      { name: 'Gray', value: '#6b7280' },
      { name: 'Grey', value: '#6b7785' },
      { name: 'Slate', value: '#64748b' },
      { name: 'Neutral', value: '#737373' },
      { name: 'Zinc', value: '#71717a' },
      { name: 'Stone', value: '#78716c' },
      { name: 'Mauve', value: '#8e8c99' },
      { name: 'Sand', value: '#8d8d86' },
    ],
  },
})
