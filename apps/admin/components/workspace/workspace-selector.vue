<script lang="ts" setup>
const {
  active,
  workspaces,
} = await useUserWorkspaces()

const open = ref(false)

function switchTo(path: string) {
  open.value = false

  navigateTo({
    path: `/${path.startsWith('/') ? path.slice(1) : path}`,
  })
}
</script>

<template>
  <UiPopover v-model:open="open">
    <UiPopoverTrigger
      class="group flex-(~ y-center between) w-full bg-transparent rounded px-2.5 py-2 transition-colors hover:bg-dimmed/75 data-[state=open]:bg-dimmed/75"
    >
      <div class="flex-(~ y-center) gap-1.5">
        <UiIcon :name="active?.icon" class="size-4.5 c-primary-300" />
        <span class="font-semibold text-(sm highlighted)">{{ active?.title }}</span>
      </div>

      <UiIcon name="ri:expand-up-down-line" class="c-muted group-hover:c-text" />
    </UiPopoverTrigger>

    <UiPopoverContent :side-offset="20" align="start" class="w-80 p-0 overflow-hidden ring ring-dimmed/65 shadow-md">
      <div class="grid-(~ cols-2) p-3 gap-2 shadow">
        <WorkspaceItem
          v-for="ws in workspaces"
          :key="ws.id"
          v-bind="ws"
          @click="switchTo(ws.path || ws.id)"
        />
      </div>
    </UiPopoverContent>
  </UiPopover>
</template>
