import type { VariantProps } from 'tailwind-variants'

export const useChipVariants = tv({
  slots: {
    root: 'relative flex-(inline center shrink-0) text-base',
    base: 'flex-(~ center) ring-(1 panel) rounded-full z-popover',
    value: 'select-none text-[11px] font-medium whitespace-nowrap',
  },
  variants: {
    variant: { solid: '', fill: 'ring-0' },
    color: { primary: '', secondary: '', info: '', success: '', warning: '', danger: '' },
    position: {
      tr: 'abs top-0 right-0',
      tl: 'abs top-0 left-0',
      br: 'abs bottom-0 right-0',
      bl: 'abs bottom-0 left-0',
    },
    dotted: {
      true: 'size-2',
      false: 'min-w-4 h-4 p-1',
    },
    inset: {
      true: 'transform',
    },
  },
  compoundVariants: [
    { variant: 'solid', color: 'primary', class: 'bg-primary' },
    { variant: 'solid', color: 'secondary', class: 'bg-toned' },
    { variant: 'solid', color: 'info', class: 'bg-info' },
    { variant: 'solid', color: 'success', class: 'bg-success' },
    { variant: 'solid', color: 'warning', class: 'bg-warning' },
    { variant: 'solid', color: 'danger', class: 'bg-danger' },

    { variant: 'fill', color: 'primary', class: 'c-primary' },
    { variant: 'fill', color: 'secondary', class: 'c-secondary' },
    { variant: 'fill', color: 'info', class: 'c-info' },
    { variant: 'fill', color: 'success', class: 'c-success' },
    { variant: 'fill', color: 'warning', class: 'c-warning' },
    { variant: 'fill', color: 'danger', class: 'c-danger' },

    { variant: 'solid', dotted: false, color: 'primary', class: 'text-primary-50' },
    { variant: 'solid', dotted: false, color: 'secondary', class: 'text-primary-50' },
    { variant: 'solid', dotted: false, color: 'info', class: 'text-info-50' },
    { variant: 'solid', dotted: false, color: 'success', class: 'text-success-50' },
    { variant: 'solid', dotted: false, color: 'warning', class: 'text-warning-50' },
    { variant: 'solid', dotted: false, color: 'danger', class: 'text-danger-50' },

    { inset: false, position: 'tr', class: '-translate-y-1/4 translate-x-1/4' },
    { inset: false, position: 'tl', class: '-translate-y-1/4 -translate-x-1/4' },
    { inset: false, position: 'br', class: 'translate-y-1/4 translate-x-1/4' },
    { inset: false, position: 'bl', class: 'translate-y-1/4 -translate-x-1/4' },
  ],
  defaultVariants: {
    variant: 'solid',
    color: 'primary',
    position: 'tr',
    dotted: false,
    inset: false,
  },
})

export type ChipVariantProps = VariantProps<typeof useChipVariants>
