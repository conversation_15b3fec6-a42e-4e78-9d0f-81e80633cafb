<script lang="ts" setup>
import type { ChipProps } from './chip.props'
import { isNumber } from '@xiaoshop/shared-schema'
import { Primitive } from 'reka-ui'
import { useChipVariants } from './chip.ui'

defineOptions({
  inheritAttrs: false,
})

const props = withDefaults(defineProps<ChipProps>(), {
  as: 'span',
  visible: true,
  variant: 'solid',
  position: 'tr',
  hideZero: false,
  dotted: false,
  inset: false,
})

const ui = useChipVariants({
  ...props,
  inset: toBoolValue(props.inset),
  dotted: toBoolValue(props.dotted),
})

const isVisible = computed(() => {
  if (!props.visible)
    return false

  if (props.hideZero) {
    return props.value && isNumber(props.value)
      ? Number(props.value) > 0
      : true
  }

  return props.visible
})

const showValue = computed(() => {
  return isNumber(props.value) && props.maxValue
    ? `${Math.min(Number(props.value), props.maxValue)}+`
    : props.value
})
</script>

<template>
  <Primitive
    :as="as"
    :as-child="asChild"
    :class="ui.root()"
  >
    <div
      v-if="isVisible"
      :class="cn(ui.base(), props.class)"
    >
      <slot name="value">
        <span v-if="value" :class="ui.value()">
          {{ showValue }}
        </span>
      </slot>
    </div>
    <slot />
  </Primitive>
</template>
