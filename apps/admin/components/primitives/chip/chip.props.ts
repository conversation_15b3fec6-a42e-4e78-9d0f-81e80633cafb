import type { ChipVariantProps } from './chip.ui'
import type { UiPrimitiveProps } from '~/types'

export interface ChipProps extends UiPrimitiveProps {
  value?: string | number
  variant?: ChipVariantProps['variant']
  color?: ChipVariantProps['color']
  position?: ChipVariantProps['position']
  dotted?: ChipVariantProps['dotted']
  inset?: ChipVariantProps['inset']
  maxValue?: number
  visible?: boolean
  hideZero?: boolean
}
