import type { VariantProps } from 'tailwind-variants'

export const useSpaceVariants = tv({
  base: 'flex',
  variants: {
    orientation: {
      horizontal: 'flex-row',
      vertical: 'flex-col',
    },
    align: {
      start: 'items-start',
      center: 'items-center',
      end: 'items-end',
      baseline: 'items-baseline',
    },
    size: {
      xs: 'gap-1',
      sm: 'gap-2',
      md: 'gap-4',
      lg: 'gap-6',
      xl: 'gap-8',
    },
    breakLine: { true: 'flex-wrap' },
  },
  defaultVariants: {
    orientation: 'horizontal',
    align: 'center',
    size: 'md',
    breakLine: false,
  },
})

export type SpaceVariantsProps = VariantProps<typeof useSpaceVariants>
