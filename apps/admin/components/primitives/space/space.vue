<script lang="ts" setup>
import type { SpaceProps } from './space.props'
import { Primitive } from 'reka-ui'
import Separator from '~/components/separator/separator.vue'
import { useSpaceVariants } from './space.ui'

const props = withDefaults(defineProps<SpaceProps>(), {
  as: 'div',
  orientation: 'horizontal',
  size: 'md',
  breakLine: false,
})

const getChildSlots = useChildSlots()
const children = getChildSlots()

const ui = useSpaceVariants({
  ...props,
  breakLine: toBoolValue(props.breakLine),
})
</script>

<template>
  <Primitive
    :as="as"
    :as-child="asChild"
    :class="cn(ui, props.class)"
  >
    <template v-for="(child, index) in children" :key="index">
      <component :is="child" />
      <slot name="separator">
        <Separator
          v-if="index < children.length - 1"
          :orientation="orientation === 'horizontal' ? 'vertical' : 'horizontal'"
          :class="orientation === 'horizontal' ? 'h-4' : 'w-4'"
        />
      </slot>
    </template>
  </Primitive>
</template>
