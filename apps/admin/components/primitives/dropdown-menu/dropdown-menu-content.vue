<script lang="ts" setup>
import type { DropdownMenuContentEmits } from 'reka-ui'
import type { DropdownMenuContentProps } from './dropdown-menu-content.props'
import type { DropdownMenuItem } from './dropdown-menu.props'
import { isArrayOfArray } from '@xiaoshop/schema'
import {
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuLabel,
  DropdownMenuPortal,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  useForwardPropsEmits,
} from 'reka-ui'

import { useDropdownMenuVariants } from './dropdown-menu.ui'

const props = defineProps<DropdownMenuContentProps>()
const emits = defineEmits<DropdownMenuContentEmits>()

const delegated = reactiveOmit(props, 'items', 'sub', 'class', 'defer', 'forceMount', 'to', 'disabled')
const forwarded = useForwardPropsEmits(delegated, emits)

const ui = useDropdownMenuVariants(props)

const groups = computed<DropdownMenuItem[][]>(() =>
  props.items?.length
    ? isArrayOfArray(props.items)
      ? props.items
      : [props.items]
    : [],
)
</script>

<template>
  <DropdownMenuPortal
    :force-mount="forceMount"
    :disabled="disabled"
    :defer="defer"
    :to="to"
  >
    <component
      :is="sub ? DropdownMenuSubContent : DropdownMenuContent"
      v-bind="forwarded"
      :class="cn(ui.content(), props.class)"
    >
      <DropdownMenuGroup
        v-for="(group, groupIdx) in groups"
        :key="`g-${groupIdx}`"
        :class="ui.group()"
      >
        <template v-for="(item, idx) in group" :key="`g-${groupIdx}-${idx}`">
          <DropdownMenuLabel v-if="item.type === 'label'" :class="ui.label()">
            {{ item.title }}
          </DropdownMenuLabel>

          <DropdownMenuSeparator v-else-if="item.type === 'separator'" :class="ui.separator()" />

          <DropdownMenuSub v-else-if="item.children?.length">
            <DropdownMenuSubTrigger :class="ui.item()">
              {{ item.title }}
            </DropdownMenuSubTrigger>

            <DropdownMenuSubContent
              :items="item.children"
              :disabled="disabled"
              :class="props.class"
              sub
            />
          </DropdownMenuSub>
        </template>
      </DropdownMenuGroup>
      <slot />
    </component>
  </DropdownMenuPortal>
</template>
