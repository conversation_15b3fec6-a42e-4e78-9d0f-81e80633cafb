import type { DropdownMenuContentEmits, DropdownMenuContentProps, DropdownMenuRootProps } from 'reka-ui'
import type { ArrayOrNested, EmitsToProps, UiProps } from '~/types'

export interface DropdownMenuItem {
  title?: string
  value?: string
  icon?: string
  type?: 'label' | 'link' | 'checkbox' | 'radio' | 'separator'
  shortcuts?: string[]
  disabled?: boolean
  checked?: boolean
  href?: string
  target?: string
  children?: ArrayOrNested<DropdownMenuItem>
  content?: Omit<DropdownMenuContentProps, 'as' | 'asChild' | 'forceMount'> & Partial<EmitsToProps<DropdownMenuContentEmits>>
  onSelect?: (e: Event) => void
  onUpdateChecked?: (checked: boolean) => void
}

export interface DropdownMenuProps extends DropdownMenuRootProps, UiProps {
  items?: ArrayOrNested<DropdownMenuItem>
  content?: Omit<DropdownMenuContentProps, 'as' | 'asChild' | 'forceMount'> & Partial<EmitsToProps<DropdownMenuContentEmits>>
  disabled?: boolean
  portal?: boolean
}
