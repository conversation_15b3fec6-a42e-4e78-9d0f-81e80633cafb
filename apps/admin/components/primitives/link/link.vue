<script lang="ts" setup>
import type { LinkProps } from './link.props'
import { useLinkVariants } from './link.ui'

const props = withDefaults(defineProps<LinkProps>(), {
  variant: 'primary',
  underlined: false,
})

const ui = useLinkVariants({
  variant: props.variant,
  underlined: toBoolValue(props.underlined),
})
</script>

<template>
  <NuxtLink :class="cn(ui, props.class)">
    <slot v-if="icon || $slots.icon" name="icon">
      <UiIcon :name="icon" :class="iconClass" />
    </slot>
    <slot />
  </NuxtLink>
</template>
