<script lang="ts" setup>
import type { ToastRootEmits } from 'reka-ui'
import type { ToastProps } from './toast.props'
import { ToastAction, ToastClose, ToastDescription, ToastRoot, ToastTitle, useForwardPropsEmits } from 'reka-ui'
import { useToastVariants } from './toast.ui'

const props = withDefaults(defineProps<ToastProps>(), {
  orientation: 'vertical',
  closeable: true,
})
const emits = defineEmits<ToastRootEmits>()

defineSlots<{
  leading: (props: any) => any
  title: (props: any) => any
  description: (props: any) => any
  actions: (props: any) => any
  close: (props: any) => any
}>()

const delegated = reactivePick(props, 'as', 'defaultOpen', 'open', 'duration', 'type')
const forwarded = useForwardPropsEmits(delegated, emits)

const background = useTheme('background')
const ui = useToastVariants({
  ...props,
  translucent: background.value === 'translucent',
})

const el = ref()
const height = ref(0)

onMounted(() => {
  if (!el.value) {
    return
  }

  setTimeout(() => {
    height.value = el.value.$el.getBoundingClientRect()?.height
  }, 0)
})

defineExpose({
  height,
})
</script>

<template>
  <ToastRoot
    ref="el"
    v-bind="forwarded"
    :data-orientation="orientation"
    :class="cn(ui.root(), props.class)"
    :style="{ '--height': height }"
  >
    <slot name="leading">
      <UiIcon
        v-if="icon"
        :name="icon"
        :class="ui.icon()"
      />
    </slot>

    <div :class="ui.wrapper()">
      <ToastTitle v-if="title || !!$slots.title" :class="ui.title()">
        <slot name="title">
          {{ title }}
        </slot>
      </ToastTitle>

      <ToastDescription v-if="description || !!$slots.description" :class="ui.description()">
        <slot name="description">
          {{ description }}
        </slot>
      </ToastDescription>

      <div v-if="orientation === 'vertical' && actions?.length" :class="ui.actions()">
        <slot name="actions">
          <ToastAction
            v-for="(action, index) in actions"
            :key="index"
            :alt-text="action.text || 'Action'"
            as-child
            @click.stop
          >
            <UiButton v-bind="action" size="xs" :color="color" />
          </ToastAction>
        </slot>
      </div>
    </div>

    <div v-if="(orientation === 'horizontal' && actions?.length) || closeable" :class="ui.actions({ orientation: 'horizontal' })">
      <template v-if="orientation === 'horizontal' && actions?.length">
        <slot name="actions">
          <ToastAction
            v-for="(action, index) in actions"
            :key="index"
            :alt-text="action.text || 'Action'"
            as-child
            @click.stop
          >
            <UiButton v-bind="action" size="xs" :color="color" />
          </ToastAction>
        </slot>
      </template>

      <ToastClose v-if="closeable || !!$slots.close" as-child>
        <slot name="close">
          <UiButton v-if="closeable" icon="mingcute:close-line" color="secondary" variant="ghost" size="xs" radius="full" square @click.stop />
        </slot>
      </ToastClose>
    </div>
  </ToastRoot>
</template>
