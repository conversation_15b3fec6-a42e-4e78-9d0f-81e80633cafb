<script lang="ts" setup>
import type { ToasterProps } from './toast-provider.props'
import { ToastPortal, ToastProvider, ToastViewport, useForwardProps } from 'reka-ui'
import { useToasterVariants } from './toast-provider.ui'
import Toast from './toast.vue'

const props = withDefaults(defineProps<ToasterProps>(), {
  position: 'br',
  duration: 5000,
})

const swipeDirection = computed<ToasterProps['swipeDirection']>(() => {
  switch (props.position) {
    case 'tc':
      return 'up'
    case 'bc':
      return 'down'
    case 'bl':
    case 'tl':
      return 'left'
  }

  return 'right'
})

const delegated = reactivePick(props, 'duration', 'label', 'swipeThreshold')
const forwarded = useForwardProps(delegated)
const ui = useToasterVariants({
  ...props,
  swipeDirection: swipeDirection.value,
})

const { toasts, remove } = useToast()
const expanded = ref(false)

const refs = ref<{ height: number }[]>([])
const height = computed(() => refs.value.reduce((acc, { height }) => acc + height + 16, 0))
const frontHeight = computed(() => refs.value[refs.value.length - 1]?.height || 0)

function getOffset(index: number) {
  return refs.value.slice(index + 1).reduce((acc, { height }) => acc + height + 16, 0)
}

function onUpdateOpen(value: boolean, id: string | number) {
  if (value) {
    return
  }

  remove(id)
}
</script>

<template>
  <ToastProvider
    :swipe-direction="swipeDirection"
    v-bind="forwarded"
  >
    <Toast
      v-for="(toast, index) in toasts"
      v-bind="omit(toast, ['id'])"
      ref="refs"
      :key="toast.id"
      :data-expanded="expanded"
      :data-front="!expanded && index === toasts.length - 1"
      :style="{
        '--index': (index - toasts.length) + toasts.length,
        '--before': toasts.length - 1 - index,
        '--offset': getOffset(index),
        '--scale': expanded ? '1' : 'calc(1 - var(--before) * var(--scale-factor))',
        '--translate': expanded ? 'calc(var(--offset) * var(--translate-factor))' : 'calc(var(--before) * var(--gap))',
        '--transform': 'translateY(var(--translate)) scale(var(--scale))',
      }"
      :class="ui.toast()"
      @update:open="onUpdateOpen($event, toast.id)"
      @click="toast.onClick && toast.onClick(toast)"
    />

    <ToastPortal>
      <ToastViewport
        :data-expanded="expanded"
        :style="{
          '--scale-factor': '0.05',
          '--translate-factor': position?.startsWith('t') ? '1px' : '-1px',
          '--gap': position?.startsWith('t') ? '16px' : '-16px',
          '--front-height': `${frontHeight}px`,
          '--height': `${height}px`,
        }"
        :class="cn(ui.viewport(), props.class)"
        @mouseenter="expanded = true"
        @mouseleave="expanded = false"
      />
    </ToastPortal>
  </ToastProvider>
</template>
