import type { ToastRootProps } from 'reka-ui'
import type { ToastVariantProps } from './toast.ui'
import type { ButtonProps } from '~/components/ui/button/button.props'
import type { UiProps } from '~/types'

export interface ToastProps extends ToastRootProps, UiProps {
  title?: string
  description?: string
  icon?: string
  orientation?: ToastVariantProps['orientation']
  color?: ToastVariantProps['color']
  actions?: ButtonProps[]
  closeable?: boolean
}
