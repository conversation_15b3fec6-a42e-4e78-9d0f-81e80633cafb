import type { VariantProps } from 'tailwind-variants'

export const useToasterVariants = tv({
  slots: {
    viewport: 'fixed flex-(~ col) z-popover w-[calc(100%-2rem)] sm:w-96 data-[expanded=true]:h-$height focus:outline-none',
    toast: [
      'abs inset-x-0 z-$index',
      'translate-y-$translate scale-$scale transform',
      '[&[data-expanded="false"]]:[&[data-front="false"]]:h-front-height',
      'children:[&[data-expanded="false"]]:[&[data-front="false"]]:invisible',
      'data-[state=closed]:animate-toast-closed',
      '[&[data-state="closed"][data-expanded="false"][data-front="false"]]:animate-toast-collapsed',
      'data-[swipe=move]:transition-none property-[transform,height] duration-200 ease-out',
    ],
  },
  variants: {
    position: {
      tc: {
        viewport: 'left-1/2 transform -translate-x-1/2',
      },
      tr: {
        viewport: 'right-4',
      },
      br: {
        viewport: 'right-4',
      },
      bc: {
        viewport: 'left-1/2 transform -translate-x-1/2',
      },
      bl: {
        viewport: 'left-4',
      },
      tl: {
        viewport: 'left-4',
      },
    },
    swipeDirection: {
      up: {
        toast: 'data-[swipe=end]:animate-toast-slide-up',
      },
      down: {
        toast: 'data-[swipe=end]:animate-toast-slide-down',
      },
      left: {
        toast: 'data-[swipe=end]:animate-toast-slide-left',
      },
      right: {
        toast: 'data-[swipe=end]:animate-toast-slide-right',
      },
    },
  },
  compoundVariants: [
    { position: ['tl', 'tc', 'tr'], class: { viewport: 'top-4', toast: 'top-0 data-[state=open]:animate-(slide-in-down duration-200 ease-in-out)' } },
    { position: ['bl', 'bc', 'br'], class: { viewport: 'bottom-4', toast: 'bottom-0 data-[state=open]:animate-(slide-in-up duration-200 ease-in-out)' } },
    { swipeDirection: ['left', 'right'], class: { toast: 'data-[swipe=move]:translate-x-$reka-toast-swipe-move-x data-[swipe=end]:translate-x-$reka-toast-swipe-end-x data-[swipe=cancel]:translate-x-0' } },
    { swipeDirection: ['up', 'down'], class: { toast: 'data-[swipe=move]:translate-y-$reka-toast-swipe-move-y data-[swipe=end]:translate-y-$reka-toast-swipe-end-y data-[swipe=cancel]:translate-y-0' } },
  ],
  defaultVariants: {
    position: 'br',
  },
})

export type ToasterVariantsProps = VariantProps<typeof useToasterVariants>
