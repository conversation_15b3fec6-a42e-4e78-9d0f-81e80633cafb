import type { VariantProps } from 'tailwind-variants'

export const useTooltipVariants = tv({
  base: 'px-2 py-1 rounded-sm overflow-hidden bg-tooltip text-(xs gray-50) select-none z-popover animate-popover',
  variants: {
    side: {
      top: 'animate-popover-t',
      right: 'animate-popover-r',
      bottom: 'animate-popover-b',
      left: 'animate-popover-l',
    },
  },
  defaultVariants: {
    side: 'bottom',
  },
})

export type TooltipVariantsProps = VariantProps<typeof useTooltipVariants>
