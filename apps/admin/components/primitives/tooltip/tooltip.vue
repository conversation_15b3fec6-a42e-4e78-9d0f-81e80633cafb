<script lang="ts" setup>
import type { TooltipRootEmits } from 'reka-ui'
import type { TooltipProps } from './tooltip.props'

import {
  TooltipContent,
  TooltipPortal,
  TooltipProvider,
  TooltipRoot,
  TooltipTrigger,
  useForwardPropsEmits,
} from 'reka-ui'
import { useTooltipVariants } from './tooltip.ui'

const props = withDefaults(defineProps<TooltipProps>(), {
  align: 'center',
  side: 'bottom',
  sideOffset: 6,
  defaultOpen: false,
  delayDuration: 200,
})

const emits = defineEmits<TooltipRootEmits>()

const providerProps = reactiveOmit(
  props,
  'content',
  'align',
  'class',
  'side',
  'sideOffset',
  'defaultOpen',
)
const rootProps = reactiveOmit(
  props,
  'content',
  'align',
  'class',
  'side',
  'sideOffset',
  'skipDelayDuration',
)

const forwarded = useForwardPropsEmits(rootProps, emits)
const ui = useTooltipVariants({ side: props.side })
</script>

<template>
  <TooltipProvider v-bind="providerProps">
    <TooltipRoot v-bind="forwarded">
      <TooltipTrigger as-child>
        <slot />
      </TooltipTrigger>
      <TooltipPortal>
        <TooltipContent
          v-bind="$attrs"
          :align="align"
          :side="side"
          :side-offset="sideOffset"
          :class="cn(ui, props.class)"
        >
          {{ content }}
        </TooltipContent>
      </TooltipPortal>
    </TooltipRoot>
  </TooltipProvider>
</template>
