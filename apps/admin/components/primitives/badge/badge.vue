<script lang="ts" setup>
import type { BadgeProps } from './badge.props'
import { Primitive } from 'reka-ui'
import { useBadgeVariants } from './badge.ui'

defineOptions({
  inheritAttrs: false,
})

const props = withDefaults(defineProps<BadgeProps>(), {
  as: 'span',
})

const delegated = reactivePick(props, 'as', 'asChild')
const ui = useBadgeVariants(props)
</script>

<template>
  <Primitive v-bind="delegated" :class="cn(ui, props.class)">
    <slot v-if="icon || $slots.icon" name="icon">
      <UiIcon :name="icon" />
    </slot>

    <slot>
      <template v-if="text">
        {{ text }}
      </template>
    </slot>
  </Primitive>
</template>
