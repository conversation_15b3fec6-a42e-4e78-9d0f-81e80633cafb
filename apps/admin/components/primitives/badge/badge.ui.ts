import type { VariantProps } from 'tailwind-variants'

export const useBadgeVariants = tv({
  base: 'flex-(inline y-center)',
  variants: {
    variant: { solid: '', soft: '', outline: '' },
    color: { primary: '', secondary: '', info: '', success: '', warning: '', danger: '' },
    radius: {
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full',
    },
    size: {
      xs: 'text-xs px-1.5 py-0.5 gap-0.5',
      sm: 'text-xs px-2 py-0.75 gap-0.5',
      md: 'text-sm px-2.5 py-1 gap-0.75',
      lg: 'text-base px-3 py-1.5 gap-1',
    },
  },
  compoundVariants: [
    { variant: 'solid', color: 'primary', class: 'bg-primary text-primary-50' },
    { variant: 'solid', color: 'secondary', class: 'bg-muted text-primary-50' },
    { variant: 'solid', color: 'info', class: 'bg-info text-info-50' },
    { variant: 'solid', color: 'success', class: 'bg-success text-success-50' },
    { variant: 'solid', color: 'warning', class: 'bg-warning text-warning-50' },
    { variant: 'solid', color: 'danger', class: 'bg-danger text-danger-50' },

    { variant: 'soft', color: 'primary', class: 'bg-primary/15 text-primary' },
    { variant: 'soft', color: 'secondary', class: 'bg-dimmed text-text' },
    { variant: 'soft', color: 'info', class: 'bg-info/15 text-info' },
    { variant: 'soft', color: 'success', class: 'bg-success/15 text-success' },
    { variant: 'soft', color: 'warning', class: 'bg-warning/15 text-warning' },
    { variant: 'soft', color: 'danger', class: 'bg-danger/15 text-danger' },

    { variant: 'outline', color: 'primary', class: 'ring-(1 border) text-primary' },
    { variant: 'outline', color: 'secondary', class: 'ring-(1 border) text-text' },
    { variant: 'outline', color: 'info', class: 'ring-(1 info) text-info' },
    { variant: 'outline', color: 'success', class: 'ring-(1 success) text-success' },
    { variant: 'outline', color: 'warning', class: 'ring-(1 warning) text-warning' },
    { variant: 'outline', color: 'danger', class: 'ring-(1 danger) text-danger' },
  ],
  defaultVariants: {
    variant: 'solid',
    color: 'primary',
    radius: 'full',
    size: 'sm',
  },
})

export type BadgeVariantProps = VariantProps<typeof useBadgeVariants>
