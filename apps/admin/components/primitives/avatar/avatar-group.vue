<script lang="ts" setup>
import type { AvatarGroupProps } from './avatar-group.props'
import { Primitive } from 'reka-ui'
import { useAvatarGroupVariants } from './avatar-group.ui'

const props = withDefaults(defineProps<AvatarGroupProps>(), {
  as: 'span',
  color: 'secondary',
  size: 'md',
  radius: 'full',
  delay: 100,
})

const getChildSlots = useChildSlots()
const slots = getChildSlots()

const ui = useAvatarGroupVariants({
  size: props.size,
})
</script>

<template>
  <Primitive
    :as="as"
    :as-child="asChild"
    :class="cn(ui.root(), props.class)"
  >
    <template v-for="(slot, index) in slots" :key="index">
      <component
        :is="slot"
        :color="color"
        :size="size"
        :radius="radius"
        :class="ui.children()"
      />
    </template>
  </Primitive>
</template>
