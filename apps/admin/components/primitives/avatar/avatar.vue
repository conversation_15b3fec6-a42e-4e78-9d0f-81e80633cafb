<script lang="ts" setup>
import type { AvatarProps } from './avatar.props'
import { AvatarFallback, AvatarImage, AvatarRoot } from 'reka-ui'
import { useAvatarVariants } from './avatar.ui'

defineOptions({
  inheritAttrs: false,
})

const props = withDefaults(defineProps<AvatarProps>(), {
  color: 'secondary',
  size: 'md',
  radius: 'full',
  delay: 100,
})

const ui = useAvatarVariants(props)
</script>

<template>
  <AvatarRoot :class="cn(ui.root(), props.class)">
    <AvatarImage v-if="src" :src="src" :alt="alt" :class="ui.image()" />

    <AvatarFallback :delay-ms="delay" :class="ui.fallback()">
      <slot>
        {{ fallback }}
      </slot>
    </AvatarFallback>
  </AvatarRoot>
</template>
