import type { VariantProps } from 'tailwind-variants'

export const useAvatarVariants = tv({
  slots: {
    root: 'relative flex-(inline shrink-0 center) overflow-hidden select-none',
    image: 'size-full object-cover rounded-inherit',
    fallback: 'flex-(~ center) size-full font-semibold leading-none',
  },
  variants: {
    size: {
      'xs': { root: 'size-5', fallback: 'size-5 text-2.5' },
      'sm': { root: 'size-6', fallback: 'size-6 text-3' },
      'md': { root: 'size-8', fallback: 'size-8 text-3' },
      'lg': { root: 'size-10', fallback: 'size-10 text-3.5' },
      'xl': { root: 'size-16', fallback: 'size-16 text-6' },
      '2xl': { root: 'size-20', fallback: 'size-20 text-10' },
      '3xl': { root: 'size-24', fallback: 'size-24 text-12' },
      '4xl': { root: 'size-32', fallback: 'size-32 text-16' },
    },
    color: {
      primary: { root: 'bg-primary text-primary-50' },
      secondary: { root: 'bg-dimmed text-toned' },
      info: { root: 'bg-info text-info-50' },
      success: { root: 'bg-success text-success-50' },
      warning: { root: 'bg-warning text-warning-50' },
      danger: { root: 'bg-danger text-danger-50' },
    },
    radius: {
      sm: { root: 'rounded-sm' },
      md: { root: 'rounded-md' },
      lg: { root: 'rounded-lg' },
      full: { root: 'rounded-full' },
    },
  },
  defaultVariants: {
    color: 'secondary',
    size: 'md',
    radius: 'full',
  },
})

export type AvatarVariantsProps = VariantProps<typeof useAvatarVariants>
