<script lang="ts" setup>
import type { LabelProps } from './label.props'
import { Primitive } from 'reka-ui'
import { useLabelVariants } from './label.ui'

const props = withDefaults(defineProps<LabelProps>(), {
  as: 'span',
  color: 'secondary',
  size: 'md',
})

const slots = useSlots()

const ui = useLabelVariants({
  ...props,
  icon: slots.indicator !== undefined || toBoolValue(props.icon) as boolean,
  dotted: toBoolValue(props.dotted),
  ring: toBoolValue(props.ring),
})
</script>

<template>
  <Primitive
    :as="as"
    :as-child="asChild"
    :class="ui.root()"
  >
    <span :class="cn(ui.dot(), props.class)">
      <slot v-if="$slots.indicator || icon" name="indicator">
        <UiIcon v-if="icon" :name="icon" />
      </slot>
    </span>
    <slot>
      {{ text }}
    </slot>
  </Primitive>
</template>
