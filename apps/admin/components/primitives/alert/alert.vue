<script lang="ts" setup>
import type { AlertProps } from './alert.props'
import { useAlertVariants } from './alert.ui'

const props = withDefaults(defineProps<AlertProps>(), {
  variant: 'solid',
  color: 'primary',
  radius: 'sm',
})

const emits = defineEmits(['close'])
const visible = defineModel<boolean>({ default: true })
const iconComputed = computed(() => {
  if (props.type && !props.icon) {
    switch (props.type) {
      case 'info':
        return 'mingcute:information-fill'
      case 'success':
        return 'mingcute:check-circle-fill'
      case 'warning':
        return 'mingcute:warning-fill'
      case 'danger':
        return 'mingcute:alert-fill'
    }
  }

  return props.icon
})

const colorComputed = computed(() => {
  if (props.type)
    return props.type as AlertProps['color']

  return props.color
})

const ui = useAlertVariants({
  ...props,
  color: colorComputed.value,
})

function handleClose() {
  nextTick(() => {
    visible.value = false
  })

  emits('close')
}
</script>

<template>
  <AnimatePresence>
    <Motion
      v-if="visible"
      as="div"
      :initial="{ opacity: 0 }"
      :animate="{ opacity: 1 }"
      :exit="{ opacity: 0 }"
      :class="cn(ui.root(), props.class)"
    >
      <slot name="indicator">
        <UiIcon v-if="iconComputed" :name="iconComputed" :class="ui.indicator()" />
      </slot>
      <div class="space-y-1.5 grow-1">
        <h5 :class="ui.title()">
          <slot name="title">
            {{ title }}
          </slot>
        </h5>
        <div v-if="$slots.content || content" :class="ui.content()">
          <slot name="content">
            {{ content }}
          </slot>
        </div>
      </div>
      <slot v-if="closeable" name="close">
        <div :class="ui.close()" @click="handleClose">
          <UiIcon name="mingcute:close-line" />
        </div>
      </slot>
    </Motion>
  </AnimatePresence>
</template>
