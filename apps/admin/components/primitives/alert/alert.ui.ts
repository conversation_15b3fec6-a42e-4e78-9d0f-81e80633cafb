import type { VariantProps } from 'tailwind-variants'

export const useAlertVariants = tv({
  slots: {
    root: 'relative flex gap-2 p-4 transition',
    indicator: 'size-4',
    title: 'text-base font-medium tracking-tight leading-none',
    content: 'text-sm leading-none',
    close: 'abs top-2 right-2 flex-(~ center) size-4.5 rounded-full hover:bg-white/20 cursor-link transition-colors [&>.iconify]:(size-3.5)',
  },
  variants: {
    variant: { solid: '', soft: '', surface: '', outline: '', bordered: { root: 'b-l-5' } },
    color: { primary: '', secondary: '', info: '', success: '', warning: '', danger: '' },
    radius: {
      none: { root: 'rounded-none' },
      sm: { root: 'rounded-sm' },
      md: { root: 'rounded-md' },
      lg: { root: 'rounded-lg' },
    },
  },
  compoundVariants: [
    { variant: 'solid', color: 'primary', class: { root: 'bg-primary c-primary-50', content: 'c-primary-50/85' } },
    { variant: 'solid', color: 'secondary', class: { root: 'bg-dimmed c-highlighted', content: 'c-text/85' } },
    { variant: 'solid', color: 'info', class: { root: 'bg-info c-info-50', content: 'c-info-200' } },
    { variant: 'solid', color: 'success', class: { root: 'bg-success c-success-50', content: 'c-success-200' } },
    { variant: 'solid', color: 'warning', class: { root: 'bg-warning c-warning-50', content: 'c-warning-200' } },
    { variant: 'solid', color: 'danger', class: { root: 'bg-danger c-danger-50', content: 'c-danger-200' } },

    { variant: 'soft', color: 'primary', class: { root: 'bg-primary/20 c-primary', content: 'c-primary/65' } },
    { variant: 'soft', color: 'secondary', class: { root: 'bg-dimmed/50 c-highlighted', content: 'c-text/65' } },
    { variant: 'soft', color: 'info', class: { root: 'bg-info/15 c-info', content: 'c-info/65' } },
    { variant: 'soft', color: 'success', class: { root: 'bg-success/15 c-success', content: 'c-success/65' } },
    { variant: 'soft', color: 'warning', class: { root: 'bg-warning/15 c-warning', content: 'c-warning/65' } },
    { variant: 'soft', color: 'danger', class: { root: 'bg-danger/15 c-danger', content: 'c-danger/65' } },

    { variant: 'surface', color: 'primary', class: { root: 'b bg-primary/15 b-primary/25 c-primary', content: 'c-primary/65' } },
    { variant: 'surface', color: 'secondary', class: { root: 'b bg-dimmed/25 c-text', content: 'c-text/65' } },
    { variant: 'surface', color: 'info', class: { root: 'b bg-info/15 b-info/25 c-info', content: 'c-info/65' } },
    { variant: 'surface', color: 'success', class: { root: 'b bg-success/15 b-success/25 c-success', content: 'c-success/65' } },
    { variant: 'surface', color: 'warning', class: { root: 'b bg-warning/15 b-warning/25 c-warning', content: 'c-warning/65' } },
    { variant: 'surface', color: 'danger', class: { root: 'b bg-danger/15 b-danger/25 c-danger', content: 'c-danger/65' } },

    { variant: 'outline', color: 'primary', class: { root: 'b b-primary/25 c-primary', content: 'c-primary/65' } },
    { variant: 'outline', color: 'secondary', class: { root: 'b c-highlighted', content: 'c-text/65' } },
    { variant: 'outline', color: 'info', class: { root: 'b b-info/25 c-info', content: 'c-info/65' } },
    { variant: 'outline', color: 'success', class: { root: 'b b-success/25 c-success', content: 'c-success/65' } },
    { variant: 'outline', color: 'warning', class: { root: 'b b-warning/25 c-warning', content: 'c-warning/65' } },
    { variant: 'outline', color: 'danger', class: { root: 'b b-danger/25 c-danger', content: 'c-danger/65' } },

    { variant: 'bordered', color: 'primary', class: { root: 'b b-l-primary b-primary/25 c-primary', content: 'c-primary/65' } },
    { variant: 'bordered', color: 'secondary', class: { root: 'b b-l-secondary b-secondary/25 c-secondary', content: 'c-secondary/65' } },
    { variant: 'bordered', color: 'info', class: { root: 'b b-l-info b-info/25 c-info', content: 'c-info/65' } },
    { variant: 'bordered', color: 'success', class: { root: 'b b-l-success b-success/25 c-success', content: 'c-success/65' } },
    { variant: 'bordered', color: 'warning', class: { root: 'b b-l-warning b-warning/25 c-warning', content: 'c-warning/65' } },
    { variant: 'bordered', color: 'danger', class: { root: 'b b-l-danger b-danger/25 c-danger', content: 'c-danger/65' } },
  ],
  defaultVariants: {
    variant: 'solid',
    color: 'primary',
    radius: 'sm',
  },
})

export type AlertVariantsProps = VariantProps<typeof useAlertVariants>
