<script lang="ts" setup>
import type { ModalContentProps } from './modal-content.props'
import { Primitive } from 'reka-ui'
import { useModalContentVariants } from './modal-content.ui'

const props = defineProps<ModalContentProps>()
const ui = useModalContentVariants(props)
</script>

<template>
  <Primitive
    :as="as"
    :as-child="asChild"
    :class="cn(ui, props.class)"
  >
    <slot />
  </Primitive>
</template>
