<script lang="ts" setup>
import type { ModalFooterProps } from './modal-footer.props'
import { Primitive } from 'reka-ui'
import { useModalFooterVariants } from './modal-footer.ui'

const props = defineProps<ModalFooterProps>()
const ui = useModalFooterVariants(props)
</script>

<template>
  <Primitive
    :as="as"
    :as-child="asChild"
    :class="cn(ui, props.class)"
  >
    <slot />
  </Primitive>
</template>
