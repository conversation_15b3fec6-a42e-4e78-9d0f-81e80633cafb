import type { VariantProps } from 'tailwind-variants'

export const useModalVariants = tv({
  slots: {
    base: 'fixed flex-(~ col) z-modal rounded-lg overflow-hidden focus:outline-none',
    overlay: 'overlay',
    close: 'abs top-4.5 right-5 rounded-full flex-(~ center) size-6 transition-colors leading-none bg-transparent hover:bg-surface/25',
  },
  variants: {
    side: {
      center: 'min-w-sm abs-center',
      top: 'min-h-sm left-0 top-0 right-0 rounded-t-0',
      right: 'min-w-sm right-2 top-2 bottom-2',
      bottom: 'min-h-sm left-0 right-0 bottom-0 rounded-b-0',
      left: 'min-w-sm left-2 top-2 bottom-2',
    },
    translucent: {
      true: {
        base: 'card-translucent',
        overlay: 'overlay-translucent',
      },
      false: {
        base: 'card-solid',
        overlay: 'overlay-solid',
      },
    },
    hideOverlay: {
      true: {
        base: 'shadow-tiny',
      },
    },
  },
  defaultVariants: {
    side: 'center',
    translucent: false,
    hideOverlay: false,
  },
})

export type ModalVariantsProps = VariantProps<typeof useModalVariants>
