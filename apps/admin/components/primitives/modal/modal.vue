<script lang="ts" setup>
import type { DialogRootEmits } from 'reka-ui'
import type { ModalProps } from './modal.props'
import { DialogClose, DialogContent, DialogOverlay, DialogPortal, DialogRoot, DialogTrigger, useForwardPropsEmits } from 'reka-ui'
import { useModalVariants } from './modal.ui'

const props = withDefaults(defineProps<ModalProps>(), {
  modal: true,
  side: 'center',
  preventClose: false,
})
const emits = defineEmits<DialogRootEmits>()

const delegated = reactiveOmit(props, 'class', 'side', 'preventClose', 'disabled', 'defer', 'forceMount', 'to')
const forwarded = useForwardPropsEmits(delegated, emits)

const background = useTheme('background')
const ui = useModalVariants({
  ...props,
  translucent: background.value === 'translucent',
  hideOverlay: !toBoolValue(props.modal),
})

const motions = {
  center: {
    hidden: { opacity: 0, top: '45%' },
    visible: { opacity: 1, top: '50%' },
  },
  top: {
    hidden: { opacity: 0, top: '-200px' },
    visible: { opacity: 1, top: '0' },
  },
  right: {
    hidden: { opacity: 0, right: '-200px' },
    visible: { opacity: 1, right: '12px' },
  },
  bottom: {
    hidden: { opacity: 0, bottom: '-200px' },
    visible: { opacity: 1, bottom: '0' },
  },
  left: {
    hidden: { opacity: 0, left: '-200px' },
    visible: { opacity: 1, left: '12px' },
  },
}

function onClickOutside(e: any) {
  if (props.preventClose)
    e.preventDefault()
}
</script>

<template>
  <DialogRoot v-bind="forwarded">
    <DialogTrigger as-child>
      <slot />
    </DialogTrigger>

    <DialogPortal
      :defer="defer"
      :force-mount="forceMount"
      :disabled="disabled"
      :to="to"
    >
      <AnimatePresence>
        <DialogOverlay
          :force-mount="forceMount"
          :class="ui.overlay()"
          as-child
        >
          <Motion
            :initial="{ opacity: 0 }"
            :animate="{ opacity: 1 }"
            :exit="{ opacity: 0 }"
          />
        </DialogOverlay>

        <DialogContent
          :class="cn(
            ui.base(),
            props.class,
          )"
          as-child
          @pointer-down-outside="onClickOutside"
        >
          <Motion
            :initial="motions[side].hidden"
            :animate="motions[side].visible"
            :exit="motions[side].hidden"
          >
            <slot name="content" />

            <DialogClose :class="ui.close()">
              <slot name="close">
                <UiIcon name="mingcute:close-line" />
              </slot>
            </DialogClose>
          </Motion>
        </DialogContent>
      </AnimatePresence>
    </DialogPortal>
  </DialogRoot>
</template>
