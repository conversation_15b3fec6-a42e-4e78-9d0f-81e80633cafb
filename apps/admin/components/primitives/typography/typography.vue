<script lang="ts" setup>
import type { TypographyProps } from './typography.props'
import { Primitive } from 'reka-ui'
import { useTypographyVariants } from './typography.ui'

const props = withDefaults(defineProps<TypographyProps>(), {
  as: 'p',
  variant: 'p',
})

const emit = defineEmits(['copied', 'edited'])

const text = defineModel<string>()
const { copy } = useClipboard()

const isEditing = ref(false)
const editValue = ref('')

watchOnce(
  text,
  () => {
    if (text.value)
      editValue.value = text.value
  },
  { immediate: true },
)

const updateEditValue = useDebounceFn(() => {
  text.value = editValue.value
  isEditing.value = false
  emit('edited', editValue.value)
}, 250)

function copyValue(value: MaybeRef<string>) {
  copy(toValue(value))
  emit('copied', toValue(value))
}

const ui = useTypographyVariants({
  variant: props.variant,
  editable: toBoolValue(props.editable),
  copyable: toBoolValue(props.copyable),
  disabled: toBoolValue(props.disabled),
})
</script>

<template>
  <Primitive
    :as="as"
    :as-child="asChild"
    :class="cn(ui, props.class)"
  >
    <template v-if="editable">
      <input
        v-if="isEditing"
        v-model="editValue"
        type="text"
        class="outline-none bg-transparent b-b px-2"
      >
      <span v-else>
        {{ editValue }}
      </span>
    </template>

    <template v-else>
      <slot>
        {{ text }}
      </slot>
    </template>

    <span v-if="copyable || editable" class="flex-(~ center) p-1 rounded hover:bg-muted">
      <slot name="copy">
        <UiIcon
          v-if="copyable && text"
          name="mingcute:copy-2-line"
          class="cursor-link"
          @click="copyValue(text)"
        />
      </slot>
      <slot name="edit">
        <UiIcon
          v-if="editable && !isEditing && text"
          name="mingcute:edit-line"
          class="cursor-link"
          @click="isEditing = true"
        />
      </slot>
      <UiIcon
        v-if="editable && isEditing && text"
        name="mingcute:check-line"
        class="text-brand cursor-link"
        @click="updateEditValue"
      />
    </span>
  </Primitive>
</template>
