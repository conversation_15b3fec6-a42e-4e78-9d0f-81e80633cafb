import type { VariantProps } from 'tailwind-variants'

export const useTypographyVariants = tv({
  variants: {
    variant: {
      h1: 'text-4xl font-bold',
      h2: 'text-3xl font-bold',
      h3: 'text-2xl font-semibold',
      h4: 'text-xl font-semibold',
      h5: 'text-lg font-semibold',
      h6: 'text-base font-semibold',
      code: 'text-sm font-mono',
      mark: 'px-2 pt-1.5 pb-0.5 bg-primary/10 text-primary leading-none rounded',
      p: 'text-base',
      span: 'text-sm',
    },
    copyable: { true: 'flex-(~ y-center gap-1.5)' },
    editable: { true: 'flex-(~ y-center gap-1.5)' },
    disabled: { true: 'is-disabled' },
  },
  compoundVariants: [
    { variant: ['h1', 'h2', 'h3', 'h4', 'h5', 'h6'], class: 'text-highlighted' },
  ],
  defaultVariants: {
    variant: 'p',
    copyable: false,
    editable: false,
    disabled: false,
  },
})

export type TypographyVariantsProps = VariantProps<typeof useTypographyVariants>
