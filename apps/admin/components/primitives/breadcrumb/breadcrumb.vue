<script lang="ts" setup>
import type { BreadcrumbProps } from './breadcrumb.props'
import { Primitive } from 'reka-ui'
import Link from '~/components/link/link.vue'
import { useBreadcrumbVariants } from './breadcrumb.ui'

const props = withDefaults(defineProps<BreadcrumbProps>(), {
  as: 'ul',
})

const delegated = reactiveOmit(props, 'class', 'items', 'separator')
const ui = useBreadcrumbVariants(props)

const isLatest = (index: number) => props.items && index === props.items.length - 1
</script>

<template>
  <Primitive v-bind="delegated" :class="cn(ui.root(), props.class)">
    <li v-for="(item, index) in items" :key="index" :class="ui.wrapper()">
      <slot name="item" v-bind="{ item, index }">
        <Link
          v-if="item.to"
          :icon="item.icon"
          :href="item.to"
          variant="secondary"
          :class="isLatest(index) && 'font-semibold text-primary hover:text-primary active:text-primary'"
        >
          {{ item.title }}
        </Link>
        <span v-else :class="cn(ui.item(), isLatest(index) && 'font-semibold text-highlighted')">
          {{ item.title }}
        </span>
      </slot>

      <div v-if="!isLatest(index)" :class="ui.separator()">
        <slot name="separator">
          {{ separator }}
        </slot>
      </div>
    </li>
  </Primitive>
</template>
