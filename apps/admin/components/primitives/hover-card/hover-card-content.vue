<script setup lang="ts">
import type { HoverCardContentProps } from 'reka-ui'
import type { UiProps } from '~/types'
import { HoverCardContent, HoverCardPortal, useForwardProps } from 'reka-ui'
import { useHoverCardContentVariants } from './hover-card-content.ui'

const props = withDefaults(defineProps<HoverCardContentProps & UiProps>(), {
  sideOffset: 4,
})
const delegated = reactiveOmit(props, 'class')
const forwarded = useForwardProps(delegated)
const background = useTheme('background')

const ui = useHoverCardContentVariants({
  ...props,
  translucent: background.value === 'translucent',
})
</script>

<template>
  <HoverCardPortal>
    <HoverCardContent
      v-bind="forwarded"
      :class="cn(ui, props.class)"
    >
      <slot />
    </HoverCardContent>
  </HoverCardPortal>
</template>
