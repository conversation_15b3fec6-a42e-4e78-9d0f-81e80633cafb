import type { VariantProps } from 'tailwind-variants'

export const useHoverCardContentVariants = tv({
  base: [
    'px-6 py-4.5 popover z-popover animate-popover',
  ],
  variants: {
    side: {
      top: 'animate-popover-t',
      right: 'animate-popover-r',
      bottom: 'animate-popover-b',
      left: 'animate-popover-l',
    },
    translucent: {
      true: 'popover-translucent',
      false: 'popover-solid',
    },
  },
  defaultVariants: {
    side: 'bottom',
    translucent: false,
  },
})

export type HoverCardContentVariantsProps = VariantProps<typeof useHoverCardContentVariants>
