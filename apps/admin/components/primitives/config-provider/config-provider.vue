<script lang="ts" setup>
import type { ConfigProviderProps } from './config-provider.props'
import { ConfigProvider } from 'reka-ui'

defineOptions({
  inheritAttrs: false,
})

const props = defineProps<ConfigProviderProps>()
const delegated = reactivePick(props, 'dir', 'locale', 'nonce', 'scrollBody', 'useId')
</script>

<template>
  <ConfigProvider v-bind="delegated">
    <slot />
  </ConfigProvider>
</template>
