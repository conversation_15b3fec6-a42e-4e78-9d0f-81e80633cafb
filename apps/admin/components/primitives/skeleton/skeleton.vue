<script lang="ts" setup>
import type { UiPrimitiveProps } from '~/types'
import { Primitive } from 'reka-ui'
import { useSkeletonVariants } from './skeleton.ui'

const props = defineProps<UiPrimitiveProps>()
const delegated = reactiveOmit(props, 'class')
const ui = useSkeletonVariants(props)
</script>

<template>
  <Primitive v-bind="delegated" :class="cn(ui, props.class)">
    <slot />
  </Primitive>
</template>
