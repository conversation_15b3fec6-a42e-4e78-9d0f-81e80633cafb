<script lang="ts" setup>
import type { SeparatorProps } from './separator.props'
import { Separator } from 'reka-ui'
import { useSeparatorVariants } from './separator.ui'

const props = withDefaults(defineProps<SeparatorProps>(), {
  orientation: 'horizontal',
})

const delegatedProps = reactiveOmit(props, 'class', 'label')

const ui = useSeparatorVariants(props)
</script>

<template>
  <Separator
    v-bind="delegatedProps"
    :class="cn(ui.base(), props.class)"
  >
    <span v-if="props.label" :class="ui.label()">
      <slot>
        {{ props.label }}
      </slot>
    </span>
  </Separator>
</template>
