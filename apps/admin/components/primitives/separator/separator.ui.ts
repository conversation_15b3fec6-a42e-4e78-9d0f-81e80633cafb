import type { VariantProps } from 'tailwind-variants'

export const useSeparatorVariants = tv({
  slots: {
    base: 'shrink-0 bg-border relative',
    label: 'abs-(~ center) flex-(~ center) text-xs text-toned bg-panel',
  },
  variants: {
    orientation: {
      horizontal: {
        base: 'h-px w-full',
        label: 'h-px py-1 px-2',
      },
      vertical: {
        base: 'w-px h-full',
        label: 'w-px py-2 px-1',
      },
    },
  },
  defaultVariants: {
    orientation: 'horizontal',
  },
})

export type SeparatorVariantProps = VariantProps<typeof useSeparatorVariants>
