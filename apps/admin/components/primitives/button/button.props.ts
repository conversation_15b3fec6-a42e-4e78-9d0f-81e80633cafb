import type { ButtonVariantProps } from './button.ui'
import type { UiPrimitiveProps } from '~/types'

export interface ButtonProps extends UiPrimitiveProps {
  text?: string
  type?: 'button' | 'submit' | 'reset'
  icon?: string
  size?: ButtonVariantProps['size']
  radius?: ButtonVariantProps['radius']
  variant?: ButtonVariantProps['variant']
  color?: ButtonVariantProps['color']
  square?: ButtonVariantProps['square']
  loading?: boolean
  disabled?: boolean
  onClick?: ((event: MouseEvent) => void | Promise<void>) | Array<((event: MouseEvent) => void | Promise<void>)>
}
