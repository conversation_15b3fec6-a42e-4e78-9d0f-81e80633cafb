<script lang="ts" setup>
import type { ButtonProps } from './button.props'

import { Primitive } from 'reka-ui'
import { useButtonVariants } from './button.ui'

const props = withDefaults(defineProps<ButtonProps>(), {
  as: 'button',
  type: 'button',
  variant: 'solid',
  color: 'primary',
  size: 'sm',
  radius: 'sm',
})

const loadingState = ref(false)

const isLoading = computed(() => {
  return props.loading || loadingState.value
})

const ui = useButtonVariants({
  ...props,
  square: toBoolValue(props.square),
})

async function onClickWrapper(event: MouseEvent) {
  loadingState.value = true

  const callbacks = Array.isArray(props.onClick) ? props.onClick : [props.onClick]
  try {
    await Promise.all(callbacks.map(fn => fn?.(event)))
  }
  finally {
    loadingState.value = false
  }
}
</script>

<template>
  <Primitive
    :as="as"
    :as-child="asChild"
    :type="type"
    :disabled="disabled || isLoading"
    :class="cn(ui.base(), props.class)"
    @click="onClickWrapper"
  >
    <div v-if="isLoading" :class="ui.loading()" />
    <UiIcon v-else-if="icon" :name="icon" />
    <slot>
      {{ text }}
    </slot>
  </Primitive>
</template>
