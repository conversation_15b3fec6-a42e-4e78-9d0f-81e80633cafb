<script lang="ts" setup>
import type { PopoverContentEmits, PopoverContentProps } from 'reka-ui'
import type { UiProps } from '~/types'

import { PopoverContent, PopoverPortal, useForwardPropsEmits } from 'reka-ui'
import { usePopoverContentVariants } from './popover-content.ui'

defineOptions({
  inheritAttrs: false,
})

const props = withDefaults(defineProps<UiProps & PopoverContentProps>(), {
  align: 'center',
  sideOffset: 6,
})
const emits = defineEmits<PopoverContentEmits>()

const delegated = reactiveOmit(props, 'class')
const forwarded = useForwardPropsEmits(delegated, emits)
const background = useTheme('background')

const ui = usePopoverContentVariants({
  ...props,
  translucent: background.value === 'translucent',
})
</script>

<template>
  <PopoverPortal>
    <PopoverContent
      v-bind="{ ...forwarded, ...$attrs }"
      :class="cn(ui, props.class)"
    >
      <slot />
    </PopoverContent>
  </PopoverPortal>
</template>
