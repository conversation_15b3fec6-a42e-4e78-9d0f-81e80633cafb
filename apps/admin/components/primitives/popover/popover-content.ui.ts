import type { VariantProps } from 'tailwind-variants'

export const usePopoverContentVariants = tv({
  base: [
    'p-4 popover animate-popover min-w-40',
  ],
  variants: {
    side: {
      top: 'animate-popover-t',
      right: 'animate-popover-r',
      bottom: 'animate-popover-b',
      left: 'animate-popover-l',
    },
    translucent: {
      true: 'popover-translucent',
      false: 'popover-solid',
    },
  },
  defaultVariants: {
    side: 'bottom',
    translucent: false,
  },
})

export type PopoverContentVariantsProps = VariantProps<typeof usePopoverContentVariants>
