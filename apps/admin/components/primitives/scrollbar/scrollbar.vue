<script lang="ts" setup>
import type { ScrollbarProps } from './scrollbar.props'
import {
  ScrollAreaCorner,
  ScrollAreaRoot,
  ScrollAreaScrollbar,
  ScrollAreaThumb,
  ScrollAreaViewport,
  useForwardExpose,
} from 'reka-ui'
import { useScrollbarVariants } from './scrollbar.ui'

defineOptions({
  inheritAttrs: false,
})

const props = defineProps<ScrollbarProps>()
const delegated = reactiveOmit(props, 'class')

const ui = useScrollbarVariants()

useForwardExpose()
</script>

<template>
  <ScrollAreaRoot
    v-bind="delegated"
    :class="cn(ui.root(), props.class)"
  >
    <ScrollAreaViewport :class="ui.viewport()">
      <slot />
    </ScrollAreaViewport>

    <ScrollAreaScrollbar orientation="horizontal" :class="ui.scrollbar()">
      <ScrollAreaThumb :class="ui.thumb()" />
    </ScrollAreaScrollbar>

    <ScrollAreaScrollbar orientation="vertical" :class="ui.scrollbar()">
      <ScrollAreaThumb :class="ui.thumb()" />
    </ScrollAreaScrollbar>

    <ScrollAreaCorner />
  </ScrollAreaRoot>
</template>
