import type { VariantProps } from 'tailwind-variants'

export const useScrollbarVariants = tv({
  slots: {
    root: 'overflow-hidden',
    viewport: 'size-full scroll-smooth rounded-inherit',
    scrollbar: 'flex select-none touch-none bg-transparent data-[orientation=vertical]:(w-2 mr-1) data-[orientation=horizontal]:(flex-col h-2 mb-1)',
    thumb: 'flex-1 bg-toned/50 rounded relative hover:bg-toned before:(content-[""] abs abs-center size-full min-w-10 min-h-10 transform rounded)',
  },
})

export type ScrollbarVariantProps = VariantProps<typeof useScrollbarVariants>
