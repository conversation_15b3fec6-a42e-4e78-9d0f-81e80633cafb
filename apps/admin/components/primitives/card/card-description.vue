<script lang="ts" setup>
import type { UiPrimitiveProps } from '~/types'
import { Primitive } from 'reka-ui'

const props = withDefaults(defineProps<UiPrimitiveProps>(), { as: 'p' })
const delegated = reactiveOmit(props, 'class')
</script>

<template>
  <Primitive
    v-bind="delegated"
    :class="cn(
      'text-(sm surface) mt-1.5 leading-none',
      props.class,
    )"
  >
    <slot />
  </Primitive>
</template>
