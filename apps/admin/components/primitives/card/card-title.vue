<script lang="ts" setup>
import type { UiPrimitiveProps } from '~/types'
import { Primitive } from 'reka-ui'

const props = withDefaults(defineProps<UiPrimitiveProps>(), { as: 'h2' })
const delegated = reactiveOmit(props, 'class')
</script>

<template>
  <Primitive
    v-bind="delegated"
    :class="cn(
      'text-base font-semibold leading-none tracking-tight',
      props.class,
    )"
  >
    <slot />
  </Primitive>
</template>
