<script lang="ts" setup>
defineProps<{
  title: string
  icon: string
  path?: string
  activeIcon?: string
}>()
</script>

<template>
  <UiTooltip :content="title" side="right" :side-offset="16">
    <NuxtLink
      v-slot="{ isActive }"
      :class="cn(
        'flex-(~ center) size-10 rounded c-primary-300 transition-colors',
        '[&.is-active]:(c-primary-50 bg-gradient-to-br from-primary-400 via-primary to-primary-400)',
        'hover:(c-primary-50 bg-gradient-to-br from-primary-400 via-primary to-primary-400)',
      )"
      :to="path"
    >
      <UiIcon :name="isActive ? activeIcon || icon : icon" class="size-5" />
    </NuxtLink>
  </UiTooltip>
</template>
