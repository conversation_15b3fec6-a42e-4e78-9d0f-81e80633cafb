<script lang="ts" setup>
</script>

<template>
  <aside class="sticky top-0 h-screen w-14 bg-gradient-to-b from-primary to-primary-600 shadow-lg z-panel">
    <div class="flex-(~ col y-center) size-full">
      <div class="flex-(~ center) w-14 h-13">
        <h1 class="i-xiao-logo size-5 c-primary-50" />
      </div>

      <UiScrollbar class="w-full h-[calc(100%-3.25rem)]">
        <nav class="flex-(~ col y-center) gap-2.5 py-2">
          <LayoutActivityItem title="控制台" icon="mingcute:home-1-line" path="/" />
          <LayoutActivityItem title="商品" icon="mingcute:box-3-line" path="/goods" />
          <LayoutActivityItem title="库存" icon="mingcute:inventory-line" path="/inventory" />
          <LayoutActivityItem title="订单" icon="mingcute:shopping-bag-3-line" path="/orders" />
          <LayoutActivityItem title="物流" icon="mingcute:truck-line" path="/logistics" />
          <LayoutActivityItem title="会员" icon="mingcute:vip-1-line" path="/members" />
          <LayoutActivityItem title="营销" icon="mingcute:red-packet-line" path="/marketing" />
          <LayoutActivityItem title="财务" icon="mingcute:bank-card-line" path="/finance" />
          <LayoutActivityItem title="统计" icon="mingcute:chart-line-line" path="/statistics" />
        </nav>
      </UiScrollbar>
    </div>
  </aside>
</template>
