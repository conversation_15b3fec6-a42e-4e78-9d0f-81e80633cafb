<script lang="ts" setup>
import type { UiProps } from '~/types'

const props = defineProps<UiProps>()

const background = useTheme('background')
const bgClass = computed(() => {
  return background.value === 'solid'
    ? 'bg-panel'
    : 'bg-panel/85 backdrop-blur'
})
</script>

<template>
  <header :class="cn('sticky top-0 h-14 shadow z-[90]', bgClass, props.class)">
    <div class="flex-(~ items-stretch gap-2) h-full">
      <div v-if="$slots.start" class="flex-(~ y-center gap-4) w-60 px-2">
        <slot name="start" />
      </div>

      <div class="flex-(~ 1 y-center gap-4)">
        <slot name="middle" />
      </div>

      <div class="flex-(~ y-center justify-end gap-4) px-4">
        <slot name="end" />
      </div>
    </div>
  </header>
</template>
