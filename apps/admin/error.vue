<script lang="ts" setup>
import type { NuxtError } from '#app'

const { error } = defineProps<{
  error: Partial<NuxtError>
}>()

const errorCodes: Record<number, string> = {
  404: '页面未找到',
  500: '服务器错误',
}

const state = ref<'error' | 'reloading'>('error')
const message = error.message ?? errorCodes[error.statusCode!] ?? '未知错误'

async function reload() {
  state.value = 'reloading'

  try {
    clearError({
      redirect: '/',
    })
  }
  catch (err) {
    console.error(err)
    state.value = 'error'
  }
}
</script>

<template>
  <UiConfigProvider>
    <NuxtLoadingIndicator color="rgb(var(--primary) / 1)" />

    <NuxtLayout>
      <div class="flex-(~ col center gap-y-8) pt-40">
        <h3 class="abs font-(display bold) text-7xl mt-20">
          {{ error.statusCode }}
        </h3>

        <svg xmlns="http://www.w3.org/2000/svg" fill="none" width="400" height="220">
          <defs>
            <linearGradient id="prefix__c" x1=".5" y1="-.023" x2=".5" y2="1.011">
              <stop offset="0%" stop-color="#FDFEFF" />
              <stop offset="99.64%" stop-color="#ECF0F5" />
            </linearGradient>
            <linearGradient id="prefix__d" x1="-.053" y1="-.108" x2=".392" y2=".378">
              <stop offset="0%" stop-color="#BCCBE1" />
              <stop offset="99.42%" stop-color="#FFF" stop-opacity="0" />
            </linearGradient>
            <linearGradient id="prefix__e" x1="-.047" y1=".271" x2=".346" y2=".424">
              <stop offset="0%" stop-color="#E2E8F0" />
              <stop offset="99.42%" stop-color="#FFF" stop-opacity="0" />
            </linearGradient>
            <linearGradient id="prefix__g" x1=".5" y1="-.023" x2=".5" y2="1.011">
              <stop offset="0%" stop-color="#FDFEFF" />
              <stop offset="99.64%" stop-color="#ECF0F5" />
            </linearGradient>
            <linearGradient id="prefix__h" x1="-.053" y1="-.108" x2=".392" y2=".378">
              <stop offset="0%" stop-color="#BCCBE1" />
              <stop offset="99.42%" stop-color="#FFF" stop-opacity="0" />
            </linearGradient>
            <linearGradient id="prefix__i" x1="-.047" y1=".271" x2=".346" y2=".424">
              <stop offset="0%" stop-color="#E2E8F0" />
              <stop offset="99.42%" stop-color="#FFF" stop-opacity="0" />
            </linearGradient>
            <filter id="prefix__b" filterUnits="objectBoundingBox" color-interpolation-filters="sRGB" x="-.746" y="-.877" width="2.492" height="3.34">
              <feFlood flood-opacity="0" result="BackgroundImageFix" />
              <feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" />
              <feOffset dy="11" />
              <feGaussianBlur stdDeviation="11" />
              <feColorMatrix values="0 0 0 0 0.3960784375667572 0 0 0 0 0.47843137383461 0 0 0 0 0.5764706134796143 0 0 0 0.27000001072883606 0" />
              <feBlend in2="BackgroundImageFix" result="effect1_dropShadow" />
              <feBlend in="SourceGraphic" in2="effect1_dropShadow" result="shape" />
            </filter>
            <filter id="prefix__f" filterUnits="objectBoundingBox" color-interpolation-filters="sRGB" x="-.99" y="-1.164" width="2.979" height="4.104">
              <feFlood flood-opacity="0" result="BackgroundImageFix" />
              <feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" />
              <feOffset dy="11" />
              <feGaussianBlur stdDeviation="11" />
              <feColorMatrix values="0 0 0 0 0.3960784375667572 0 0 0 0 0.47843137383461 0 0 0 0 0.5764706134796143 0 0 0 0.27000001072883606 0" />
              <feBlend in2="BackgroundImageFix" result="effect1_dropShadow" />
              <feBlend in="SourceGraphic" in2="effect1_dropShadow" result="shape" />
            </filter>
            <clipPath id="prefix__a">
              <rect width="400" height="220" rx="0" />
            </clipPath>
          </defs>
          <g clip-path="url(#prefix__a)">
            <path d="M179.827 88.765q0 1.922-.094 3.842-.095 1.92-.284 3.832-.188 1.913-.47 3.814-.284 1.901-.659 3.786-.376 1.885-.844 3.75-.468 1.864-1.027 3.704-.559 1.838-1.208 3.648-.649 1.81-1.386 3.585-.737 1.776-1.56 3.513-.824 1.738-1.73 3.433-.909 1.695-1.899 3.343-.99 1.649-2.06 3.247-1.07 1.598-2.218 3.142-1.147 1.543-2.369 3.03-1.22 1.485-2.515 2.909-1.293 1.424-2.655 2.783-1.362 1.359-2.789 2.65t-2.915 2.51q-1.489 1.22-3.036 2.364-1.547 1.145-3.148 2.213-1.6 1.068-3.253 2.055-1.652.989-3.35 1.895-1.699.906-3.44 1.728-1.74.821-3.52 1.557t-3.593 1.383q-1.813.647-3.656 1.205-1.843.558-3.71 1.025-1.869.467-3.758.842-1.889.375-3.794.657-1.905.282-3.821.47-1.917.189-3.84.283-1.924.094-3.85.094-1.926 0-3.85-.094-1.923-.094-3.84-.283-1.916-.188-3.821-.47-1.905-.282-3.794-.657-1.889-.375-3.757-.842-1.868-.467-3.711-1.025-1.843-.558-3.657-1.205-1.813-.647-3.592-1.383-1.78-.736-3.52-1.557-1.741-.822-3.44-1.728-1.698-.906-3.35-1.895-1.652-.987-3.253-2.055-1.602-1.068-3.149-2.213-1.546-1.144-3.035-2.364-1.489-1.219-2.916-2.51-1.427-1.291-2.789-2.65-1.361-1.359-2.655-2.783-1.293-1.424-2.515-2.909-1.222-1.487-2.369-3.03-1.147-1.544-2.217-3.142-1.07-1.598-2.06-3.247-.99-1.648-1.898-3.343-.908-1.695-1.731-3.433-.824-1.737-1.56-3.513-.738-1.775-1.387-3.585-.648-1.81-1.207-3.648-.56-1.84-1.027-3.704-.468-1.865-.844-3.75-.376-1.885-.658-3.786-.283-1.901-.472-3.814-.189-1.912-.283-3.832-.095-1.92-.095-3.842 0-1.922.095-3.841.094-1.92.283-3.833.189-1.912.472-3.814.282-1.9.658-3.786.376-1.885.844-3.75.468-1.864 1.027-3.703.559-1.839 1.207-3.649.65-1.81 1.386-3.585.737-1.775 1.56-3.513.824-1.737 1.732-3.432.908-1.695 1.898-3.344.99-1.648 2.06-3.246 1.07-1.599 2.217-3.142 1.147-1.544 2.37-3.03 1.22-1.485 2.514-2.91 1.294-1.424 2.655-2.783 1.362-1.359 2.79-2.65 1.426-1.29 2.915-2.51 1.489-1.219 3.035-2.364 1.547-1.145 3.149-2.212 1.601-1.068 3.253-2.056 1.652-.988 3.35-1.894 1.699-.906 3.44-1.728 1.74-.822 3.52-1.557 1.78-.736 3.592-1.383 1.814-.648 3.657-1.206 1.843-.558 3.71-1.025 1.869-.467 3.758-.842 1.889-.375 3.794-.657 1.905-.282 3.821-.47 1.917-.188 3.84-.283 1.924-.094 3.85-.094 1.926 0 3.85.094 1.923.095 3.84.283 1.916.188 3.821.47 1.905.282 3.794.657 1.889.375 3.757.842 1.868.467 3.711 1.025 1.843.558 3.656 1.206 1.813.647 3.593 1.383 1.78.735 3.52 1.557 1.741.822 3.44 1.728 1.698.906 3.35 1.894 1.652.988 3.253 2.056 1.601 1.067 3.148 2.212 1.547 1.145 3.036 2.365 1.488 1.219 2.915 2.51 1.427 1.29 2.79 2.65 1.361 1.358 2.654 2.782 1.294 1.425 2.515 2.91 1.222 1.486 2.37 3.03 1.147 1.543 2.217 3.141 1.07 1.599 2.06 3.247.99 1.649 1.898 3.344.907 1.695 1.731 3.432.823 1.738 1.56 3.513.737 1.776 1.386 3.585.65 1.81 1.208 3.65.56 1.838 1.027 3.703.468 1.864.844 3.75.375 1.884.658 3.785.283 1.902.471 3.814.19 1.913.284 3.833.094 1.92.094 3.841z" fill="#ffffffa3" />
            <path d="M388.08 174.684H15.69l57.973-77.682 24.86-32.945 21.702 23.489 4.177 4.473 46.663-64.464L191.035 0l15.588 22.675 53.082 77.173L281.1 75.445 292 62.94l9.273 11.083 86.806 100.662z" class="mountain-dark" />
            <path d="M98.524 64.058l-1.02 110.626H15.691l57.972-77.682 24.86-32.944zM191.035 0l-3.363 163.093-63.27-71.074L191.035 0zM293.938 158.72l-34.233-58.872 32.297-36.909 1.936 95.782z" class="mountain-light" />
            <path d="M388.08 176.177H400v-2.986H0v2.986h388.08zM236.17 192.649h-25.88v-2.987h25.88v2.987zM307.489 192.649H281.61v-2.987h25.879v2.987zM271.32 192.649h-5.502v-2.987h5.501v2.987zM186.653 192.649H93.938v-2.987h92.715v2.987zM66.532 192.649H54.814v-2.987h11.718v2.987zM385.227 192.649h-32.604v-2.987h32.604v2.987zM37.087 192.649H5.4v-2.987h31.687v2.987zM133.062 206.375H30.668v-2.987h102.394v2.987zM265.818 206.375h-72.236v-2.987h72.236v2.987zM375.243 206.375h-72.237v-2.987h72.237v2.987zM179.215 206.375h-15.792v-2.987h15.792v2.987zM330.922 220h-87.417v-2.987h87.417V220zM210.29 220h-57.667v-2.987h57.668V220zM133.062 220h-17.116v-2.987h17.116V220zM89.863 220h-37.8v-2.987h37.8V220z" fill-rule="evenodd" class="water" />
            <path d="M136.016 148.349c0 6.406-5.196 9.253-11.512 9.253-6.317 0-11.513-2.847-11.513-9.253q0-6.405 11.513-45.958 11.512 39.654 11.512 45.958z" class="tree-leaves" />
            <path d="M123.402 173.464v-36.3h2v36.3h-2z" fill-rule="evenodd" class="tree-trunk" />
            <path d="M129.25 140.445l-4.177 3.761-1.342-1.476 4.177-3.762 1.341 1.477zM123.637 152.752l-6.012-7.118 1.531-1.281 6.011 7.118-1.53 1.28z" fill-rule="evenodd" class="tree-trunk" />
            <path d="M239.532 145.502c0 7.118-5.807 10.27-12.838 10.27-7.132 0-12.838-3.152-12.838-10.27q0-7.118 12.838-51.144 12.838 44.128 12.838 51.144z" class="tree-leaves" />
            <path d="M225.694 173.464v-40.469h2v40.469h-2z" fill-rule="evenodd" class="tree-trunk" />
            <path d="M231.962 136.57l-4.585 4.27-1.366-1.454 4.584-4.27 1.367 1.454zM225.926 150.41l-6.725-8.032 1.537-1.275 6.724 8.033-1.536 1.274z" fill-rule="evenodd" class="tree-trunk" />
            <path d="M281.304 156.381c0 4.373-3.565 6.304-7.845 6.304-4.38 0-7.845-1.931-7.845-6.304q0-4.372 7.845-31.317 7.845 26.945 7.845 31.317z" class="tree-leaves" />
            <path d="M272.357 173.464v-24.709h2v24.709h-2z" fill-rule="evenodd" class="tree-trunk" />
            <path d="M276.877 151.227l-2.853 2.541-1.334-1.484 2.853-2.541 1.334 1.484zM272.588 159.56l-4.075-4.88 1.538-1.273 4.075 4.88-1.538 1.273z" fill-rule="evenodd" class="tree-trunk" />
            <path d="M323.383 151.5c0 5.593-4.483 8.033-10.087 8.033-5.603 0-10.087-2.44-10.087-8.032q0-5.593 10.087-40.265 10.087 34.672 10.087 40.265z" class="tree-leaves" />
            <path d="M312.296 173.464v-31.826h2v31.826h-2z" fill-rule="evenodd" class="tree-trunk" />
            <path d="M317.538 144.61l-3.566 3.253-1.352-1.468 3.567-3.254 1.35 1.468zM312.529 155.394l-5.298-6.304 1.534-1.278 5.298 6.304-1.534 1.278z" fill-rule="evenodd" class="tree-trunk" />
            <path d="M125.318 92.63l-17.523 1.525-8.151 8.337-9.78-1.729-16.2 10.067-5.095-6.61 30.26-40.162 26.49 28.571z" class="mountain-snow" opacity=".8" />
            <path d="M214.57 34.164l-8.253-1.83-8.456 3.966-6.826-1.729-6.52 11.591-10.19-4.779-11.41 4.78-1.936-4.474L191.035 0l23.535 34.164zM306.673 79.92l-8.558 6.71-7.947-4.371-11.207 6.507-3.158-6.813L292.41 62.94l14.263 16.98z" class="mountain-snow" opacity=".6" />
            <path d="M90.78 33.96c1.63-.406 3.158-.508 4.788-.508 1.529 0 3.159.102 4.687.305 1.529.203 3.158.61 4.687 1.323 1.528.711 3.056 1.728 3.973 3.457l-1.731-.408 1.833-1.423c.714-.406 1.325-.915 1.936-1.322 1.325-.813 2.751-1.525 4.28-2.033 1.527-.509 3.056-.915 4.584-1.12 1.63-.1 3.159-.1 4.788.306.306.102.408.407.306.61-.102.305-.306.407-.61.305h-.103c-1.325-.304-2.853-.203-4.28 0-1.425.204-2.852.713-4.176 1.322-1.325.61-2.547 1.322-3.77 2.135-.612.408-1.223.915-1.732 1.323l-1.63 1.423c-.51.406-1.121.305-1.528-.102l-.102.102-.102-.203c-.408-1.12-1.529-2.034-2.751-2.644a16.803 16.803 0 00-4.178-1.424 31.882 31.882 0 00-4.483-.61c-1.528-.102-3.056-.102-4.38.203h-.102c-.306.103-.51-.1-.612-.406 0-.305.102-.61.408-.61zM45.543 61.821c1.63-.407 3.158-.508 4.789-.508a36.3 36.3 0 014.686.304c1.529.204 3.159.61 4.687 1.322 1.529.712 3.056 1.729 3.974 3.457l-1.732-.407 1.834-1.423c.713-.407 1.324-.915 1.935-1.322 1.325-.813 2.751-1.525 4.279-2.034 1.529-.508 3.057-.914 4.586-1.118 1.63-.102 3.158-.102 4.787.305.307.102.409.407.307.61-.103.306-.307.407-.612.306h-.102c-1.324-.306-2.853-.205-4.28 0-1.425.203-2.851.711-4.176 1.32-1.324.611-2.547 1.323-3.77 2.136-.611.407-1.223.916-1.732 1.322l-1.63 1.423c-.51.407-1.121.306-1.528-.102l-.102-.1-.102-.204c-.407-1.119-1.529-2.034-2.751-2.643a16.803 16.803 0 00-4.178-1.425 31.882 31.882 0 00-4.482-.61c-1.529-.101-3.057-.101-4.381.204h-.102c-.306.102-.51-.101-.611-.407 0-.102.203-.406.407-.406zM101.784 57.855c1.63-.406 3.158-.508 4.787-.508 1.529 0 3.16.102 4.687.305 1.529.203 3.16.61 4.687 1.321 1.529.712 3.057 1.729 3.973 3.458l-1.731-.407 1.833-1.423c.714-.407 1.325-.916 1.936-1.322 1.325-.814 2.751-1.526 4.28-2.034 1.528-.508 3.056-.915 4.584-1.118 1.631-.101 3.159-.101 4.79.305.304.101.406.406.304.61-.1.305-.305.407-.61.305h-.102c-1.325-.306-2.853-.203-4.28 0-1.426.203-2.853.711-4.177 1.322-1.325.61-2.547 1.321-3.77 2.135-.61.406-1.222.915-1.732 1.321l-1.63 1.425c-.509.406-1.12.304-1.528-.102l-.102-.102-.102-.204c-.407-1.118-1.528-2.033-2.75-2.643a16.857 16.857 0 00-4.178-1.424 31.774 31.774 0 00-4.483-.61c-1.528-.101-3.057-.101-4.381.204h-.102c-.305.101-.51-.102-.611-.407 0-.101.102-.407.408-.407z" class="birds" />
            <g filter="url(#prefix__b)">
              <path d="M285.614 59.246c0 5.852-4.742 10.583-10.656 10.583H239.37c-7.086.153-12.746-5.495-12.746-12.314 0-6.868 5.71-12.567 12.9-12.212 6.169-19.233 34.416-16.537 36.812 3.359 5.303.661 9.28 5.14 9.28 10.584z" fill="url(#prefix__c)" />
            </g>
            <path d="M274.958 69.83c5.863 0 10.656-4.732 10.656-10.584 0-5.852-4.793-10.584-10.656-10.584s-10.656 4.732-10.656 10.584c0 5.852 4.793 10.583 10.656 10.583z" fill="url(#prefix__d)" />
            <path d="M257.571 69.931c10.453 0 18.968-8.447 18.968-18.878 0-10.43-8.515-18.877-18.968-18.877-10.452 0-18.966 8.447-18.966 18.877 0 10.431 8.463 18.878 18.966 18.878z" fill="url(#prefix__e)" />
            <g filter="url(#prefix__f)">
              <path d="M203.045 77.864c0 4.41-3.574 7.978-8.032 7.978h-26.825c-5.343.115-9.608-4.142-9.608-9.282a9.22 9.22 0 019.723-9.205c4.65-14.498 25.94-12.465 27.748 2.531 3.997.499 6.994 3.874 6.994 7.978z" fill="url(#prefix__g)" />
            </g>
            <path d="M195.013 85.841c4.42 0 8.032-3.566 8.032-7.977 0-4.411-3.612-7.978-8.032-7.978s-8.032 3.567-8.032 7.978c0 4.41 3.613 7.977 8.032 7.977z" fill="url(#prefix__h)" />
            <path d="M181.908 85.918c7.879 0 14.296-6.366 14.296-14.23 0-7.862-6.417-14.229-14.296-14.229-7.878 0-14.297 6.367-14.297 14.23s6.38 14.23 14.297 14.23z" fill="url(#prefix__i)" />
          </g>
        </svg>

        <div class="text-toned mt-8">
          {{ message }}
        </div>

        <UiButton :loading="state === 'reloading'" @click="reload">
          刷新
        </UiButton>
      </div>
    </NuxtLayout>
    <UiToastProvider />
    <NuxtRouteAnnouncer />
  </UiConfigProvider>
</template>

<style scoped>
.mountain-dark {
  @apply fill-gray-200;
}

.mountain-light {
  @apply fill-gray-200;
}

.mountain-snow {
  @apply fill-gray-50;
}

.tree-trunk {
  @apply fill-gray-400;
}

.tree-leaves {
  @apply fill-gray-600;
}

.birds {
  @apply fill-gray-400;
}

.water {
  @apply fill-gray-300;
}
</style>
