{"name": "@xiaoshop/admin", "type": "module", "version": "1.0.0", "private": true, "packageManager": "pnpm@10.12.4", "description": "XiaoShop 云链小店 (@admin)", "author": "<PERSON> <<EMAIL>>", "license": "Apache-2.0", "homepage": "https://github.com/moujinet/xiaoshop#readme", "repository": {"type": "git", "url": "git+https://github.com/moujinet/xiaoshop.git", "directory": "apps/admin"}, "bugs": {"url": "https://github.com/moujinet/xiaoshop/issues"}, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@xiaoshop/shared": "workspace:*", "clsx": "catalog:", "motion-v": "catalog:", "nuxt": "catalog:", "reka-ui": "catalog:", "tailwind-merge": "catalog:", "tailwind-variants": "catalog:", "vue": "catalog:", "vue-router": "catalog:"}, "devDependencies": {"@iconify-json/mingcute": "catalog:", "@iconify-json/ri": "catalog:", "@nuxt/icon": "catalog:", "@nuxt/image": "catalog:", "@nuxtjs/color-mode": "catalog:", "@unocss/nuxt": "catalog:", "@vueuse/nuxt": "catalog:", "std-env": "catalog:", "typescript": "catalog:", "unocss": "catalog:", "unocss-preset-animations": "catalog:"}}