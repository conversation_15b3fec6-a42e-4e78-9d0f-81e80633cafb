import { dirname, join } from 'node:path'
import { fileURLToPath } from 'node:url'
import { isDevelopment } from 'std-env'
import pkg from './package.json'

const currentDir = dirname(fileURLToPath(import.meta.url))

export default defineNuxtConfig({
  modules: [
    '@vueuse/nuxt',
    '@unocss/nuxt',
    '@nuxt/icon',
    '@nuxt/image',
    '@nuxtjs/color-mode',
  ],

  // debug: isDevelopment,

  app: {
    rootId: 'app',
    head: {
      viewport: 'width=device-width, initial-scale=1.0',
      htmlAttrs: {
        lang: 'zh-CN',
      },
      meta: [
        { name: 'msapplication-TileColor', content: '#fff' },
        { name: 'apple-mobile-web-app-status-bar-style', content: 'black-translucent' },
      ],
      link: [
        { rel: 'icon', type: 'image/svg+xml', href: '/favicon.svg' },
        { rel: 'apple-touch-icon', href: '/apple-touch-icon.png' },
      ],
    },
  },

  devtools: {
    enabled: isDevelopment,

    timeline: {
      enabled: true,
    },
  },

  compatibilityDate: '2024-11-01',

  sourcemap: isDevelopment,

  css: [
    '@unocss/reset/tailwind-compat.css',
  ],

  experimental: {
    defaults: {
      nuxtLink: {
        activeClass: 'is-active',
        exactActiveClass: 'is-exact-active',
      },
    },
  },

  future: {
    compatibilityVersion: 4,
  },

  vue: {
    propsDestructure: true,
  },

  alias: {
    '#ui': join(currentDir, 'components', 'primitives'),
  },

  components: [
    { path: './components', extensions: ['.vue'] },
    { path: './components/primitives', extensions: ['.vue'], prefix: 'Ui' },
  ],

  imports: {
    dirs: [
      './services',
    ],
    injectAtEnd: true,
  },

  runtimeConfig: {
    public: {
      version: pkg.version,
    },
  },

  vite: {
    optimizeDeps: {
      include: [
        'reka-ui',
        'reka-ui/namespaced',
        'motion-v',
      ],
    },
  },

  // @unocss/nuxt
  unocss: {
    components: false,
  },

  // @nuxt/icon
  icon: {
    componentName: 'UiIcon',
    provider: 'server',
    cssLayer: 'components',
    serverBundle: {
      remote: 'jsdelivr',
      collections: [
        'mingcute',
        'ri',
      ],
    },
  },

  // @nuxt/image
  image: {
    format: ['webp', 'jpeg', 'jpg', 'png', 'svg'],
    provider: 'ipx',
  },

  // @nuxtjs/color-mode
  colorMode: {
    preference: 'light',
    classSuffix: '',
    disableTransition: true,
  },
})
