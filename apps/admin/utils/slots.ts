import type { RendererElement, RendererNode, VNodeArrayChildren, VNodeChild } from 'vue'
import { Comment, Fragment } from 'vue'

export function useChildSlots(): () => (
  | VNode<
    RendererNode,
    RendererElement,
    {
      [key: string]: any
    }
  >
  | VNodeArrayChildren
  | VNodeChild
)[] {
  const instance = getCurrentInstance()

  return () => {
    const { slots } = instance || {}
    const content = slots?.default?.() || []

    return content
      .filter((item) => {
        if (typeof item.type === 'symbol' && !item.children) {
          return false
        }
        return item.type !== Comment
      })
      .map((item) => {
        if (item.children && Array.isArray(item.children) && item.type === Fragment)
          return item.children
        return item
      })
      .flat()
  }
}
