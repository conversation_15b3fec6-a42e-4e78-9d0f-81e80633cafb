export function useData<T>(data: MaybeRef<T | undefined>, defaultValue: T): T {
  return isRef(data)
    ? data.value ? data.value : defaultValue
    : data || defaultValue
}

export function omit<Data extends object, <PERSON> extends keyof Data>(
  data: Data,
  keys: Keys[],
): Omit<Data, Keys> {
  const result = { ...data }

  for (const key of keys) {
    delete result[key]
  }

  return result as Omit<Data, Keys>
}

export function pick<Data extends object, Keys extends keyof Data>(
  data: Data,
  keys: Keys[],
): Pick<Data, Keys> {
  return keys.reduce((acc, key) => {
    acc[key] = data[key]
    return acc
  }, {} as Pick<Data, Keys>)
}
