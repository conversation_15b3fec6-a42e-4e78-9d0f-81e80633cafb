const shaders = {
  50: withTint(0.95),
  100: withTint(0.9),
  200: withTint(0.75),
  300: withTint(0.6),
  400: withTint(0.3),
  500: (c: number[]) => c,
  600: withShade(0.9),
  700: with<PERSON>hade(0.6),
  800: with<PERSON>hade(0.45),
  900: with<PERSON>hade(0.3),
  950: withShade(0.2),
}

export function getColors(color: string) {
  const colors: Record<string, string> = {}
  const components = parseColor(color)

  for (const [name, fn] of Object.entries(shaders)) {
    colors[name] = fn(components).join(' ')
  }

  return colors
}

function parseColor(color = '') {
  if (typeof color !== 'string') {
    throw new TypeError('Color should be string!')
  }

  const hexMatch = /^#?([\da-f]{2})([\da-f]{2})([\da-f]{2})$/i.exec(color)
  if (hexMatch) {
    return hexMatch.splice(1).map(c => Number.parseInt(c, 16))
  }

  const hexMatchShort = /^#?([\da-f])([\da-f])([\da-f])$/i.exec(color)
  if (hexMatchShort) {
    return hexMatchShort.splice(1).map(c => Number.parseInt(c + c, 16))
  }

  if (color.includes(',')) {
    return color.split(',').map(p => Number.parseInt(p))
  }

  throw new Error('Invalid color format! Use #ABC or #AABBCC or r,g,b')
}

function tint(components: number[], intensity: number) {
  return components.map(c => Math.round(c + (255 - c) * intensity))
}

function shade(components: number[], intensity: number) {
  return components.map(c => Math.round(c * intensity))
}

function withTint(intensity: number) {
  return (hex: number[]) =>
    tint(hex, intensity)
}

function withShade(intensity: number) {
  return (hex: number[]) =>
    shade(hex, intensity)
}
