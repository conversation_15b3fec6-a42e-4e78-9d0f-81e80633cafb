import type { ThemeSettings } from '../types'

export function useTheme<K extends keyof ThemeSettings>(key: K): ComputedRef<ThemeSettings[K]> {
  const appConfig = useAppConfig()

  return computed({
    get: () => {
      return appConfig.theme[key]
    },
    set: (value: ThemeSettings[K]) => {
      if (appConfig.theme[key] !== value) {
        updateAppConfig({
          theme: {
            [key]: value,
          },
        })
      }

      if (['primary', 'gray'].includes(key)) {
        const colors = getColors(appConfig.theme[key])
        window.localStorage.setItem(`admin-theme-${key}-raw`, JSON.stringify(colors))
      }

      window.localStorage.setItem(`admin-theme-${key}`, appConfig.theme[key])
    },
  })
}
