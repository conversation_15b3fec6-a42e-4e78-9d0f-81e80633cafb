import type { ThemeGray, ThemePrimary, ThemeSettings } from './types'

declare module 'nuxt/schema' {
  interface AppConfigPresetOption<T> {
    name: string
    value: T
  }

  interface AppConfigInput {
    theme?: Partial<ThemeSettings>
    preset?: {
      primary?: AppConfigPresetOption<ThemePrimary>[]
      gray?: AppConfigPresetOption<ThemeGray>[]
    }
  }

  interface AppConfig {
    theme: Partial<ThemeSettings>
    preset: {
      primary?: AppConfigPresetOption<ThemePrimary>[]
      gray?: AppConfigPresetOption<ThemeGray>[]
    }
  }
}

export {}
