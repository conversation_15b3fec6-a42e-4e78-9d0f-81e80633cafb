import type { UseHeadInput } from '@unhead/vue/types'

function generateColors(name: string, color: string) {
  const colors = getColors(color)

  return Object.entries(colors).map(
    ([key, value]) => `--${name}-${key}:${value}`,
  ).join(';')
}

export default defineNuxtPlugin({
  name: 'admin-ui-colors',
  enforce: 'pre',
  setup() {
    const appConfig = useAppConfig()
    const primaryColors = computed(() => {
      const { primary } = appConfig.theme

      return `:root { ${generateColors('primary', primary)}; }`
    })

    const grayColors = computed(() => {
      const { gray } = appConfig.theme

      return `:root { ${generateColors('gray', gray)}; }`
    })

    const head: UseHeadInput = {
      style: [
        {
          innerHTML: () => primaryColors.value,
          id: 'admin-theme-primary',
          tagPriority: -2,
        },
        {
          innerHTML: () => grayColors.value,
          id: 'admin-theme-gray',
          tagPriority: -2,
        },
      ],
    }

    useHead(head)
  },
})
