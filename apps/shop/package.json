{"name": "@xiaoshop/shop", "type": "module", "private": true, "packageManager": "pnpm@10.12.4", "description": "XiaoShop 云链小店 (@shop)", "author": "<PERSON> <<EMAIL>>", "license": "Apache-2.0", "homepage": "https://github.com/moujinet/xiaoshop#readme", "repository": {"type": "git", "url": "git+https://github.com/moujinet/xiaoshop.git", "directory": "apps/shop"}, "bugs": {"url": "https://github.com/moujinet/xiaoshop/issues"}, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"nuxt": "catalog:", "vue": "catalog:", "vue-router": "catalog:"}}