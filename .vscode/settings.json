{
  "typescript.tsdk": "node_modules/typescript/lib",

  // Disable the accessibility support
  "editor.accessibilitySupport": "off",

  // Disable the default formatter, use eslint instead
  "prettier.enable": false,
  "editor.formatOnSave": false,

  // Auto fix
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.organizeImports": "never"
  },

  // Silent the stylistic rules in you IDE, but still auto fix them
  "eslint.rules.customizations": [
    { "rule": "style/*", "severity": "off" },
    { "rule": "format/*", "severity": "off" },
    { "rule": "*-indent", "severity": "off" },
    { "rule": "*-spacing", "severity": "off" },
    { "rule": "*-spaces", "severity": "off" },
    { "rule": "*-order", "severity": "off" },
    { "rule": "*-dangle", "severity": "off" },
    { "rule": "*-newline", "severity": "off" },
    { "rule": "*quotes", "severity": "off" },
    { "rule": "*semi", "severity": "off" }
  ],

  // Enable eslint for all supported languages
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact",
    "vue",
    "html",
    "markdown",
    "json",
    "jsonc",
    "yaml",
    "toml",
    "xml",
    "gql",
    "graphql",
    "astro",
    "svelte",
    "css",
    "less",
    "scss",
    "pcss",
    "postcss"
  ],

  "cSpell.words": [
    "automd",
    "Bitstream",
    "Chillax",
    "citty",
    "clsx",
    "cmdk",
    "colorjs",
    "commitizen",
    "consola",
    "Consolas",
    "cssvar",
    "demi",
    "dismissable",
    "fontshare",
    "hebilicious",
    "hoverable",
    "ianvs",
    "idcard",
    "leftbar",
    "lucide",
    "mkdirp",
    "newdot",
    "nuxtjs",
    "ohash",
    "oklab",
    "oklch",
    "Preflights",
    "reka",
    "rimraf",
    "samk",
    "scule",
    "shadcn",
    "siderbar",
    "sonner",
    "tanstack",
    "tiktok",
    "undocs",
    "unrs",
    "vaul",
    "vcalendar",
    "vitest",
    "wecom",
    "xiao",
    "xiaoshop"
  ]
}
