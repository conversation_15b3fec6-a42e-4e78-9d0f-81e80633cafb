{"name": "xia<PERSON><PERSON>", "type": "module", "version": "1.0.0", "private": true, "packageManager": "pnpm@10.12.4", "description": "XiaoShop 云链小店", "author": "<PERSON> <<EMAIL>>", "license": "Apache-2.0", "homepage": "https://github.com/moujinet/xiaoshop#readme", "repository": {"type": "git", "url": "git+https://github.com/moujinet/xiaoshop.git"}, "bugs": {"url": "https://github.com/moujinet/xiaoshop/issues"}, "scripts": {"dev": "pnpm run admin:dev", "build": "pnpm run admin:build", "admin:dev": "pnpm --filter @xiaoshop/admin run dev", "admin:build": "pnpm --filter @xiaoshop/admin run build", "docs:dev": "pnpm --filter @xiaoshop/docs run dev", "uikit:dev": "pnpm --filter @xiaoshop/uikit run dev", "lint": "eslint .", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "commit": "czg"}, "devDependencies": {"@antfu/eslint-config": "catalog:", "@commitlint/cli": "catalog:", "@commitlint/config-conventional": "catalog:", "commitizen": "catalog:", "cross-env": "catalog:", "cz-git": "catalog:", "czg": "catalog:", "eslint": "catalog:", "eslint-plugin-pnpm": "catalog:", "jsonc-eslint-parser": "catalog:", "lint-staged": "catalog:", "postcss": "catalog:", "postcss-nested": "catalog:", "rimraf": "catalog:", "simple-git-hooks": "catalog:", "std-env": "catalog:", "typescript": "catalog:", "vite": "catalog:", "yaml-eslint-parser": "catalog:"}, "simple-git-hooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.{vue,ts,tsx,js,jsx}": "eslint --fix"}, "config": {"commitizen": {"path": "node_modules/cz-git"}}}