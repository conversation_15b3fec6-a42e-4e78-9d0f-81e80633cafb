import { describe, expect, it } from 'vitest'
import { transformColor } from '../src/runtime/utils/color'

describe('colors', () => {
  it('should transform color to shaders', () => {
    const colors = [
      transformColor({ appearance: 'light', color: '#0055ff' }),
      transformColor({ appearance: 'light', color: 'color(display-p3 0.733 0.864 0.724)' }),
      transformColor({ appearance: 'light', color: 'tomato' }),
      transformColor({ appearance: 'dark', color: 'ruby' }),
    ]
    expect(colors).toMatchSnapshot()
  })
})
