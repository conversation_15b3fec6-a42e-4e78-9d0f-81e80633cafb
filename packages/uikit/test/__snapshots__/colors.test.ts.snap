// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`colors > should transform color to shaders 1`] = `
[
  [
    "oklch(99.41% 0.0023 262.6)",
    "oklch(98.27% 0.011 262.6)",
    "oklch(96.06% 0.0233 262.6)",
    "oklch(93.56% 0.0456 262.6)",
    "oklch(90.37% 0.0686 262.6)",
    "oklch(86.31% 0.0944 262.6)",
    "oklch(80.72% 0.1159 262.6)",
    "oklch(73.09% 0.1525 262.6)",
    "oklch(53.32% 0.2596 262.6)",
    "oklch(48.58% 0.2366 262.6)",
    "oklch(51.8% 0.2366 262.6)",
    "oklch(31.46% 0.1181 262.6)",
  ],
  [
    "oklch(100% none 146.8 / none)",
    "oklch(90.91% none 146.8 / none)",
    "oklch(81.82% none 146.8 / none)",
    "oklch(72.73% none 146.8 / none)",
    "oklch(63.64% none 146.8 / none)",
    "oklch(54.55% none 146.8 / none)",
    "oklch(45.45% none 146.8 / none)",
    "oklch(36.36% none 146.8 / none)",
    "oklch(85.78% 0.0714 146.8)",
    "oklch(82.65% 0.0714 146.8)",
    "oklch(9.091% none 146.8 / none)",
    "oklch(0% none 146.8 / none)",
  ],
  [
    "oklch(99.35% 0.0031 22.75)",
    "oklch(98.42% 0.0071 31.02)",
    "oklch(95.44% 0.022 30.88)",
    "oklch(92.6% 0.0471 31.76)",
    "oklch(89.21% 0.0626 31.49)",
    "oklch(85.24% 0.077 32.01)",
    "oklch(80.28% 0.0943 32.17)",
    "oklch(74.15% 0.1182 32.07)",
    "oklch(62.7% 0.1937 33.31)",
    "oklch(60.4% 0.1953 33.26)",
    "oklch(56.65% 0.1977 32.81)",
    "oklch(34.61% 0.0798 30.53)",
  ],
  [
    "oklch(18.88% 0.0137 2.904)",
    "oklch(20.77% 0.0159 5.29)",
    "oklch(25.38% 0.0602 6.544)",
    "oklch(29.21% 0.0888 5.505)",
    "oklch(33.27% 0.0997 6.398)",
    "oklch(38.2% 0.105 7.589)",
    "oklch(44.83% 0.1165 9.017)",
    "oklch(54.38% 0.1454 11.06)",
    "oklch(62.84% 0.195 13.19)",
    "oklch(66.35% 0.1794 13.69)",
    "oklch(78.61% 0.1611 17.01)",
    "oklch(90.54% 0.0527 355.8)",
  ],
]
`;
