import { readdirSync, readFileSync } from 'node:fs'
import { join } from 'node:path'

export class FileReader {
  constructor(root) {
    this.root = root
  }

  list() {
    return readdirSync(this.root)
  }

  read(name) {
    return readFileSync(join(this.root, name), 'utf8')
  }

  readAnyOf(...names) {
    let error

    for (let id = 0; id < names.length; id++) {
      const name = names[id]

      try {
        return this.read(name)
      }
      catch (e) {
        if (!error && typeof e?.code === 'string') {
          if (['EACCES', 'EPERM'].includes(e.code))
            error = e.path
        }

        if (id === names.length - 1)
          continue

        if (error)
          return `ERROR: Read File ${error} failed.`
        else
          return undefined
      }
    }
  }
}
