/* eslint-disable node/prefer-global/process */
import { existsSync, readFileSync, writeFileSync } from 'node:fs'
import { defineCommand } from 'citty'
import { consola } from 'consola'
import { mkdirp } from 'mkdirp'
import { resolve } from 'pathe'
import { renderTemplate } from '../../utils/template.mjs'

export default defineCommand({
  meta: {
    name: 'component',
    description: 'Create a new component.',
  },
  args: {
    name: {
      type: 'positional',
      required: true,
      description: 'Name of the component.',
    },
    simple: {
      type: 'boolean',
      description: 'Create a simple component without `Reka UI Primitive`.',
      alias: 's',
      default: false,
    },
  },
  async setup({ args }) {
    const name = args.name
    const components = resolve('src/runtime/components')

    consola.info(`Creating component \`${name}\`.\n`)

    let componentPath = resolve(components, name)
    let componentDirName = name
    let needsPlayground = true

    if (name.includes('-')) {
      const parentName = name.split('-').shift()

      if (
        !existsSync(resolve(components, name))
        && existsSync(resolve(components, parentName))
      ) {
        componentPath = resolve(components, parentName)
        componentDirName = parentName
        needsPlayground = false
      }
    }

    if (!existsSync(componentPath))
      await mkdirp(componentPath)

    const componentFiles = [
      'vue.hbs',
      'props.ts.hbs',
      'ui.ts.hbs',
    ]

    for (const componentFile of componentFiles) {
      const content = renderTemplate(`component/${componentFile}`, { name, isSimple: args.simple })
      const outputName = `${name}.${componentFile.replace('.hbs', '')}`

      if (!content) {
        consola.error(`Failed to render template \`${componentFile}\`.`)
        continue
      }

      await writeFile(resolve(componentPath, outputName), content)
    }

    const typesPath = resolve('src/runtime/types/components.ts')

    // Update types
    await writeFile(typesPath, `export * from '../components/${componentDirName}/${name}.props'\n`, {
      encoding: 'utf8',
      flag: 'a',
    }, true)

    // Sort
    const file = readFileSync(typesPath, 'utf8')
    const sorted = file.trim().split('\n').filter(Boolean).sort().join('\n')
    writeFileSync(typesPath, `${sorted}\n`, 'utf8')

    // Playground
    if (needsPlayground) {
      const playgroundPath = resolve('playground/app/pages/components')
      const playgroundName = `${name}.vue`
      const playgroundContent = renderTemplate('component/playground.hbs', { name })

      if (!playgroundContent) {
        consola.error(`Failed to render template \`playground.hbs\`.`)
        process.exit(1)
      }

      await writeFile(resolve(playgroundPath, playgroundName), playgroundContent)
    }
  },
})

async function writeFile(path, content, overwrite) {
  const isExists = existsSync(path)
  const outputFile = `${path.replace(resolve('.'), '.')}`

  if (!overwrite && isExists && !await consola.prompt(`File \`${outputFile}\` already exists. Overwrite?`, {
    type: 'confirm',
    initial: true,
  })) {
    consola.info(`Skipped \`${path}\`.`)
    return
  }

  writeFileSync(path, content, {
    encoding: 'utf8',
    flag: 'w',
  })

  !overwrite && consola.success(`${isExists ? 'Overwrote' : 'Generated'} \`${outputFile}\`.`)
}
