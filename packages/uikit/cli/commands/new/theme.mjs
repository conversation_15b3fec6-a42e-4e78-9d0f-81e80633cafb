/* eslint-disable node/prefer-global/process */
import { existsSync, readFileSync, writeFileSync } from 'node:fs'
import { defineCommand } from 'citty'
import { consola } from 'consola'
import { resolve } from 'pathe'
import { renderTemplate } from '../../utils/template.mjs'

export default defineCommand({
  meta: {
    name: 'theme',
    description: 'Create a new theme.',
  },
  args: {
    key: {
      type: 'positional',
      required: true,
      description: 'Key of the theme.',
    },
    name: {
      type: 'string',
      description: 'Name of the theme.',
      alias: 'n',
    },
  },
  async setup({ args }) {
    const key = args.key
    let name = args.name || key

    if (key === name) {
      name = await consola.prompt('Confirm your theme name?', {
        placeholder: name,
        initial: name,
      })
    }

    const root = resolve('.')
    const themes = resolve('src/themes')
    const themeTemplate = 'theme/theme.ts.hbs'
    const outputName = `${key}-theme.ts`

    consola.info(`Creating theme \`${name}(${key})\`.`)

    if (existsSync(resolve(themes, outputName))) {
      consola.error(`Theme \`${key}\` already exists.`)
      process.exit(1)
    }

    const content = renderTemplate(themeTemplate, { key, name })

    if (!content) {
      consola.error(`Failed to render template \`${themeTemplate}\`.`)
      process.exit(1)
    }

    writeFileSync(resolve(themes, outputName), content, {
      encoding: 'utf8',
      flag: 'w',
    })

    const indexPath = resolve('src/themes/index.ts')

    // Update index
    writeFileSync(indexPath, `export * from './${outputName.replace('.ts', '')}'\n`, {
      encoding: 'utf8',
      flag: 'a',
    })

    // Sort
    const file = readFileSync(indexPath, 'utf8')
    const sorted = file.trim().split('\n').filter(Boolean).sort().join('\n')
    writeFileSync(indexPath, `${sorted}\n`, 'utf8')

    consola.success(`Generated \`${themes.replace(root, '.')}/${outputName}\`.`)
  },
})
