import type { UiThemeDefinition } from '../types'

export default {
  id: '{{ lowerCase id }}',
  name: '{{ name }}',
  colors: {
    accent: '',
    neutral: '',
    info: '',
    success: '',
    warning: '',
    error: '',
    background: '',
  },
  tokens: {
    foreground: {},
    background: {},
    border: {},
    header: {},
    activity: {},
    brand: {},
    sidebar: {},
    panel: {},
    tooltip: {},
  },
} satisfies UiThemeDefinition
