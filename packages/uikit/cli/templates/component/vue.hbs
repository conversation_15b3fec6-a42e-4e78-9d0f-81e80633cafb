<script lang="ts" setup>
import type { {{ pascalCase name }}Props } from './{{ lowerCase name }}.props'
{{#unless isSimple}}
import { reactivePick } from '@vueuse/core'
import { Primitive } from 'reka-ui'
{{/unless}}
import { cn } from '../../utils'
import { use{{ pascalCase name }}Styles } from './{{ lowerCase name }}.ui'

const props = defineProps<{{ pascalCase name }}Props>()
{{#unless isSimple}}const delegated = reactivePick(props, 'as', 'asChild'){{/unless}}
const ui = use{{ pascalCase name }}Styles(props)
</script>

<template>
  <{{#if isSimple}}div{{else}}Primitive{{/if}}{{#unless isSimple}} v-bind="delegated"{{/unless}} :class="cn(ui, props.class)">
    <slot />
  </{{#if isSimple}}div{{else}}Primitive{{/if}}>
</template>
