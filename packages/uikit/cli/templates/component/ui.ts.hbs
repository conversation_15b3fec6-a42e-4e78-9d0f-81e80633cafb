import type { UiVariantProps } from '../../types'
import { createVariants } from '../../utils'

export const use{{ pascalCase name }}Styles = createVariants({
  variants: {},
  compoundVariants: [],
  defaultVariants: {},
})

export type {{ pascalCase name }}StyleSlots = typeof use{{ pascalCase name }}Styles['slots']
export type {{ pascalCase name }}StyleProps = UiVariantProps<typeof use{{ pascalCase name }}Styles>
