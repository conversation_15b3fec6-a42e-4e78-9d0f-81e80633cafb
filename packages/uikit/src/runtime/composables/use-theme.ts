import type { AppConfig } from 'nuxt/schema'
import type { ComputedRef } from 'vue'
import { updateAppConfig, useAppConfig } from '#app'
import { createSharedComposable } from '@vueuse/core'
import { computed } from 'vue'

type ThemeConfig = AppConfig['uikit']

function _useTheme<K extends keyof ThemeConfig>(key: K): ComputedRef<ThemeConfig[K]> {
  const appConfig = useAppConfig()

  return computed({
    get: () => {
      return appConfig.uikit[key]
    },
    set: (value: ThemeConfig[K]) => {
      if (appConfig.uikit[key] !== value) {
        updateAppConfig({
          ui: { [key]: value },
        })
      }

      window.localStorage.setItem(`ui-${key}`, JSON.stringify(appConfig.uikit[key]))
    },
  })
}

export const useTheme = /* @__PURE__ */ createSharedComposable(_useTheme)
