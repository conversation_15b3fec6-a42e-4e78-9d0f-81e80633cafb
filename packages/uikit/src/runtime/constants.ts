export const DEFAULT_SPECIAL_COLORS = ['black', 'white'] as const
export const DEFAULT_NEUTRAL_COLORS = ['gray', 'mauve', 'slate', 'sage', 'olive', 'sand'] as const
export const DEFAULT_ACCENT_COLORS = ['tomato', 'red', 'ruby', 'crimson', 'pink', 'plum', 'purple', 'violet', 'iris', 'indigo', 'blue', 'cyan', 'teal', 'jade', 'green', 'grass', 'bronze', 'gold', 'brown', 'orange', 'amber', 'yellow', 'lime', 'mint', 'sky'] as const
export const DEFAULT_COLORS = [...DEFAULT_SPECIAL_COLORS, ...DEFAULT_NEUTRAL_COLORS, ...DEFAULT_ACCENT_COLORS] as const

export const DEFAULT_APPEARANCE = 'light'
export const DEFAULT_RADIUS = '0.25rem'
export const DEFAULT_TRANSLUCENT = false
export const DEFAULT_LIGHT_BG = '#fff'
export const DEFAULT_DARK_BG = '#111'

export const DEFAULT_ICONS = {
  arrowLeft: 'ri:arrow-left-line',
  arrowRight: 'ri:arrow-right-line',
  up: 'ri:arrow-up-s-line',
  right: 'ri:arrow-right-s-line',
  down: 'ri:arrow-down-s-line',
  left: 'ri:arrow-left-s-line',
  select: 'ri:expand-up-down-line',
  close: 'ri:close-line',
  more: 'ri:more-line',
  external: 'ri:arrow-right-up-line',
  loading: 'mingcute:loading-line',
  check: 'ri:check-line',
  minus: 'ri:subtract-line',
  plus: 'ri:add-line',
  search: 'ri:search-line',
  info: 'ri:information-line',
  success: 'ri:checkbox-circle-line',
  warning: 'ri:alert-line',
  error: 'ri:close-circle-line',
}
