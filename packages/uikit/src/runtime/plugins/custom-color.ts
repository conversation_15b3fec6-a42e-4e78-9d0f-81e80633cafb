import type { UseHeadInput } from '@unhead/vue/types'
import { THEMES } from '#build/uikit/themes'
import { defineNuxtPlugin, useAppConfig, useHead, useNuxtApp } from '#imports'
import { computed } from 'vue'
import { transformToken } from '../utils/color'

const scales = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12] as const

function generateScales(appearance: 'light' | 'dark', key: string) {
  return `${scales.map(scale => `--custom-${key}-${scale}: ${transformToken({ appearance, value: `${key}.${scale}` })};`).join('\n')}`
}

export default defineNuxtPlugin(() => {
  const nuxtApp = useNuxtApp()
  const appConfig = useAppConfig()

  const {
    theme,
    radius,
    accentColor,
    neutralColor,
  } = appConfig.uikit

  const colors = computed(() => {
    const userTheme = THEMES.find(t => t.id === theme)
    const userAppearance = userTheme?.appearance || 'light'

    const cssvar = `@layer base {
      :root {
        ${accentColor ? generateScales(userAppearance, accentColor) : ''}
        ${neutralColor ? generateScales(userAppearance, neutralColor) : ''}
        ${radius ? `--custom-radius: ${radius};` : ''}
      }
    }`

    return accentColor || neutralColor || radius
      ? cssvar.replace(/\s+/g, ' ')
      : ''
  })

  const headData: UseHeadInput = {
    style: [{
      innerHTML: () => colors.value,
      tagPriority: -2,
      id: 'custom-colors',
    }],
  }

  if (import.meta.client && nuxtApp.isHydrating && !nuxtApp.payload.serverRendered) {
    const style = document.createElement('style')

    style.innerHTML = colors.value
    style.setAttribute('data-custom-colors', '')
    document.head.appendChild(style)

    headData.script = [{
      innerHTML: 'document.head.removeChild(document.querySelector(\'[data-custom-colors]\'))',
    }]
  }

  useHead(headData)
})
