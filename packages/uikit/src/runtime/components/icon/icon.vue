<script lang="ts" setup>
import type { IconProps } from './icon.props'
import { useAppConfig } from '#app'
import { reactiveOmit } from '@vueuse/core'
import { computed } from 'vue'

const props = defineProps<IconProps>()
const delegated = reactiveOmit(props, 'name')

const iconName = computed(() => {
  const defaults = useAppConfig().uikit.icons

  if (Object.keys(defaults).includes(props.name))
    return defaults[props.name as UiIconName]

  return props.name
})
</script>

<template>
  <NuxtIcon v-bind="delegated" :name="iconName" />
</template>
