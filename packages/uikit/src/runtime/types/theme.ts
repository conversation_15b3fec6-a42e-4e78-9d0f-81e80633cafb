import type { DeepPartial } from './utils'

export type UiThemeColorScales<T = string> = [T, T, T, T, T, T, T, T, T, T, T, T]

/**
 * Theme Colors
 *
 * **Color Rules**
 *
 * - `#hex|color()|oklch()...` : shaders(oklch(...))
 * - `red` : getColor() -> shaders(oklch(...))
 */
export interface UiThemeColors<T = string> {
  // --ui-accent-[1~12] : var(--custom-accent-[1~12], oklch(accent-[1~12]))
  accent: T
  // --ui-neutral-[1~12] : var(--custom-neutral-[1~12], oklch(neutral-[1~12]))
  neutral: T
  // --ui-info-[1~12]
  info: T
  // --ui-success-[1~12]
  success: T
  // --ui-warning-[1~12]
  warning: T
  // --ui-error-[1~12]
  error: T
}

/**
 * Theme Tokens
 *
 * **Tokens Rules**
 *
 * - `var(--custom-token) : var(--custom-token)`
 * - `#hex` : hexToOklch() -> oklch(...)
 * - `gray.5` -> oklch(...)
 * - `ui.neutral.5` -> var(--ui-neutral-5)
 * - `theme.foreground.dimmed` -> var(--ui-fg-dimmed)
 */
export interface UiThemeTokens {
  /**
   * Background
   *
   * - --ui-bg: bg-theme -> white
   */
  background: string
  /**
   * Foreground
   *
   * - --ui-fg-dimmed : c-dimmed -> --ui-neutral-4
   * - --ui-fg-muted : c-muted -> --ui-neutral-5
   * - --ui-fg : c-text -> --ui-neutral-11
   * - --ui-fg-contrast : c-contrast -> --ui-neutral-12
   */
  foreground: {
    dimmed: string
    muted: string
    default: string
    contrast: string
  }
  /**
   * Border
   *
   * - --ui-border : b-default -> --ui-neutral-5
   * - --ui-border-toned: b-toned -> --ui-neutral-6
   */
  border: {
    default: string
    toned: string
  }
  /**
   * Activity
   *
   * - --ui-activity-width : w-activity -> 3rem
   * - --ui-activity-fg : c-activity
   * - --ui-activity-bg : bg-activity
   */
  activity: {
    width: string
    foreground: string
    background: string
  }
  /**
   * Brand
   *
   * - --ui-brand-fg : c-brand
   * - --ui-brand-bg : bg-brand
   */
  brand: {
    foreground: string
    background: string
  }
  /**
   * Header
   *
   * - --ui-header-height : h-header -> 3.5rem
   * - --ui-header-bg : bg-header
   */
  header: {
    height: string
    background: string
  }
  /**
   * Sidebar
   *
   * - --ui-sidebar-width : w-sidebar -> 15rem
   * - --ui-sidebar-bg : bg-sidebar
   */
  sidebar: {
    width: string
    background: string
  }
  /**
   * Panel
   *
   * - --ui-panel-fg : c-panel
   * - --ui-panel-bg : bg-panel
   */
  panel: {
    foreground: string
    background: string
  }
  /**
   * Tooltip
   *
   * - --ui-tooltip-bg : bg-tooltip
   * - --ui-tooltip-fg : c-tooltip
   */
  tooltip: {
    background: string
    foreground: string
  }
}

export interface UiThemeDefinition {
  id: string
  name: string
  default?: boolean
  appearance?: 'light' | 'dark'
  translucent?: boolean
  radius?: string
  colors?: Partial<UiThemeColors>
  tokens?: DeepPartial<UiThemeTokens>
}

export interface UiTheme {
  id: string
  name: string
  appearance: 'light' | 'dark'
  translucent: boolean
  radius: string
  colors: UiThemeColors<UiThemeColorScales>
  tokens: UiThemeTokens
}
