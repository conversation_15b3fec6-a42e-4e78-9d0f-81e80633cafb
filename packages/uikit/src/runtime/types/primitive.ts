import type { PrimitiveProps } from 'reka-ui'
import type { HTMLAttributes } from 'vue'

export type { VariantProps as UiVariantProps } from 'tailwind-variants'

type SupportedColor = UiThemeColor | UiAccentColor

export declare interface UiBaseProps {
  class?: HTMLAttributes['class']
  visible?: boolean
}

export declare interface UiIconProps<T = UiIconName | string> {
  icon?: T
}

export declare interface UiColorProps<T = SupportedColor> {
  color?: T
}

export declare interface UiRadiusProps<T = 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'full'> {
  radius?: T
}

export declare interface UiSizeProps<T = 'xs' | 'sm' | 'md' | 'lg' | 'xl'> {
  size?: T
}

export declare interface UiTranslucentProps {
  translucent?: boolean
}

export declare interface UiPrimitiveProps extends UiBaseProps, PrimitiveProps {}
