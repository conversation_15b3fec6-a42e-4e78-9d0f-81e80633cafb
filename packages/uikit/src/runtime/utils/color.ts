import type { UiThemeColorScales } from '../types'
import * as RadixColors from '@radix-ui/colors'
import BezierEasing from 'bezier-easing'
import Color from 'colorjs.io'
import {
  DEFAULT_COLORS,
  DEFAULT_DARK_BG,
  DEFAULT_LIGHT_BG,
  DEFAULT_NEUTRAL_COLORS,
} from '../constants'

const lightColors = getAllColors('light')
const darkColors = getAllColors('dark')

const lightNeutralColors = getNeutralColors('light')
const darkNeutralColors = getNeutralColors('dark')

/**
 * Transform color to color scales
 */
export function transformColor(
  {
    color,
    appearance = 'light',
    isNeutral = false,
    background = appearance === 'light' ? DEFAULT_LIGHT_BG : DEFAULT_DARK_BG,
  }: {
    color: string
    appearance?: 'light' | 'dark'
    isNeutral?: boolean
    background?: string
  },
): UiThemeColorScales {
  if (DEFAULT_COLORS.includes(color as any)) {
    return getRadixColor(appearance, color, true)
  }

  const colors = isNeutral
    ? appearance === 'light' ? lightNeutralColors : darkNeutralColors
    : appearance === 'light' ? lightColors : darkColors

  return generateColor(color, colors, background)
}

/**
 * Transform token to css variable or oklch string
 */
export function transformToken({
  value,
  appearance = 'light',
}: {
  value: string
  appearance?: 'light' | 'dark'
}): string {
  if (value.startsWith('#')) {
    return toOklchString(value)
  }

  if (value.includes('.')) {
    const parts = value.split('.')
    const type = ['ui', 'theme'].includes(parts[0]) ? parts.shift() : 'color'
    const [name, shade] = parts

    if (type === 'color' && DEFAULT_COLORS.includes(name as any)) {
      const color = RadixColors[getRadixColorName(name, appearance)]
      const colorValue = Object.values(color)[Number(shade) - 1]

      return toOklchString(colorValue)
    }

    if (type === 'ui')
      return `var(--ui-${name}${shade !== 'default' ? `-${shade}` : ''})`

    if (type === 'theme') {
      const mappings: Record<string, string> = {
        foreground: 'fg',
        background: 'bg',
        border: 'border',
        header: 'header',
        activity: 'activity',
        brand: 'brand',
        sidebar: 'sidebar',
        panel: 'panel',
        tooltip: 'tooltip',
      }

      return `var(--ui-${mappings[name]}${shade !== 'default' ? `-${shade}` : ''})`
    }
  }

  return value
}

/**
 * Transform color to oklch
 */
export function toOklchString(colorOrStr: Color | string): string {
  const color = typeof colorOrStr === 'string'
    ? new Color(colorOrStr)
    : colorOrStr

  return color
    .to('oklch')
    .toString({ precision: 4 })
}

/**
 * Get radix color scales from color name
 */
function getRadixColor<
  S extends boolean = false,
  T extends S extends true ? string : Color = S extends true ? string : Color,
  RT = UiThemeColorScales<T>,
>(
  appearance: 'light' | 'dark',
  name: string,
  stringify?: boolean,
): RT {
  const colorName = getRadixColorName(name, appearance)

  return Object.values(RadixColors[colorName]).map(
    color => stringify
      ? toOklchString(color)
      : new Color(color).to('oklch'),
  ) as RT
}

/**
 * Get radix color name
 */
function getRadixColorName(name: string, appearance: 'light' | 'dark') {
  return name === 'black'
    ? 'blackP3A'
    : name === 'white'
      ? 'whiteP3A'
      : `${name}${appearance === 'light' ? 'P3' : 'DarkP3'}` as keyof typeof RadixColors
}

/**
 * Generate color scales from color string
 */
function generateColor(
  color: string,
  colors: Record<string, UiThemeColorScales<Color>>,
  background: string,
): UiThemeColorScales {
  const sourceColor = new Color(color).to('oklch')
  const backgroundColor = new Color(background).to('oklch')

  const scales = scaleColor(sourceColor, colors, backgroundColor)

  scales[8] = sourceColor
  scales[9] = getHoverColor(sourceColor, [scales])

  scales[10].coords[1] = Math.min(
    Math.max(scales[8].coords[1], scales[7].coords[1]),
    scales[10].coords[1],
  )

  scales[11].coords[1] = Math.min(
    Math.max(scales[8].coords[1], scales[7].coords[1]),
    scales[11].coords[1],
  )

  return scales.map(toOklchString) as UiThemeColorScales
}

/**
 * Get color scales from color string
 */
function scaleColor(
  source: Color,
  colors: Record<string, UiThemeColorScales<Color>>,
  backgroundColor: Color,
): UiThemeColorScales<Color> {
  const allColors: { name: string, color: Color, distance: number }[] = []

  Object.entries(colors).forEach(([name, scale]) => {
    for (const color of scale) {
      const distance = source.deltaEOK(color)
      allColors.push({ name, distance, color })
    }
  })

  allColors.sort((a, b) => a.distance - b.distance)

  const closestColors = allColors.filter(
    (color, i, arr) => i === arr.findIndex(value => value.name === color.name),
  )

  const grayColorNames = DEFAULT_NEUTRAL_COLORS as readonly string[]
  const allAreGrays = closestColors.every(color =>
    grayColorNames.includes(color.name),
  )
  if (!allAreGrays && grayColorNames.includes(closestColors[0].name)) {
    while (grayColorNames.includes(closestColors[1].name)) {
      closestColors.splice(1, 1)
    }
  }

  const closestA = closestColors[0]
  const closestB = closestColors[1]

  const a = closestB.distance
  const b = closestA.distance
  const c = closestA.color.deltaEOK(closestB.color)

  const cosA = (b ** 2 + c ** 2 - a ** 2) / (2 * b * c)
  const radA = Math.acos(cosA)
  const sinA = Math.sin(radA)

  const cosB = (a ** 2 + c ** 2 - b ** 2) / (2 * a * c)
  const radB = Math.acos(cosB)
  const sinB = Math.sin(radB)

  const tanC1 = cosA / sinA
  const tanC2 = cosB / sinB
  const ratio = Math.max(0, tanC1 / tanC2) * 0.5

  const colorA = colors[closestA.name]
  const colorB = colors[closestB.name]

  const scale = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11].map(i =>
    new Color(Color.mix(colorA[i], colorB[i], ratio)).to('oklch'),
  ) as UiThemeColorScales<Color>

  const baseColor = scale.slice()
    .sort((a, b) => source.deltaEOK(a) - source.deltaEOK(b))[0]

  const ratioC = source.coords[1] / baseColor.coords[1]

  scale.forEach((color) => {
    color.coords[1] = Math.min(
      source.coords[1] * 1.5,
      color.coords[1] * ratioC,
    )
    color.coords[2] = source.coords[2]
  })

  // Light
  if (scale[0].coords[0] > 0.5) {
    const lightnessScale = scale.map(({ coords }) => coords[0])
    const backgroundL = Math.max(0, Math.min(1, backgroundColor.coords[0]))
    const newLightnessScale = transposeProgressionStart(
      backgroundL,
      [1, ...lightnessScale],
      [0, 2, 0, 2] as [number, number, number, number],
    )

    newLightnessScale.shift()

    newLightnessScale.forEach((lightness, i) => {
      scale[i].coords[0] = lightness
    })

    return scale
  }

  // Dark
  const ease: [number, number, number, number] = [1, 0, 1, 0]
  const referenceBackgroundColorL = scale[0].coords[0]
  const backgroundColorL = Math.max(0, Math.min(1, backgroundColor.coords[0]))

  const ratioL = backgroundColorL / referenceBackgroundColorL

  if (ratioL > 1) {
    const maxRatio = 1.5

    for (let i = 0; i < ease.length; i++) {
      const metaRatio = (ratioL - 1) * (maxRatio / (maxRatio - 1))
      ease[i] = ratioL > maxRatio ? 0 : Math.max(0, ease[i] * (1 - metaRatio))
    }
  }

  const lightnessScale = scale.map(({ coords }) => coords[0])
  const newLightnessScale = transposeProgressionStart(
    backgroundColorL,
    lightnessScale,
    ease,
  )

  newLightnessScale.forEach((lightness, i) => {
    scale[i].coords[0] = lightness
  })

  return scale
}

/**
 * Get hover color
 */
function getHoverColor(source: Color, scales: UiThemeColorScales<Color>[]) {
  const [L, C, H] = source.coords
  const newL = L > 0.4 ? L - 0.03 / (L + 0.1) : L + 0.03 / (L + 0.1)
  const newC = L > 0.4 && !Number.isNaN(H) ? C * 0.93 + 0 : C
  const hoverColor = new Color('oklch', [newL, newC, H])

  let closestColor = hoverColor
  let minDistance = Infinity

  scales.forEach((scale) => {
    for (const color of scale) {
      const distance = hoverColor.deltaEOK(color)
      if (distance < minDistance) {
        minDistance = distance
        closestColor = color
      }
    }
  })

  hoverColor.coords[1] = closestColor.coords[1]
  hoverColor.coords[2] = closestColor.coords[2]

  return hoverColor
}

/**
 * Get all colors
 */
function getAllColors(appearance: 'light' | 'dark'): Record<string, UiThemeColorScales<Color>> {
  return Object.fromEntries(
    DEFAULT_COLORS.map(name => [
      name,
      getRadixColor(appearance, name),
    ]),
  )
}

/**
 * Get neutral colors
 */
function getNeutralColors(appearance: 'light' | 'dark'): Record<string, UiThemeColorScales<Color>> {
  return Object.fromEntries(
    DEFAULT_NEUTRAL_COLORS.map(name => [
      name,
      getRadixColor(appearance, name),
    ]),
  )
}

/**
 * Transpose progression start
 */
function transposeProgressionStart(
  to: number,
  arr: number[],
  curve: [number, number, number, number],
) {
  return arr.map((n, i, arr) => {
    const lastIndex = arr.length - 1
    const diff = arr[0] - to
    const fn = BezierEasing(...curve)
    return n - diff * fn(1 - i / lastIndex)
  })
}
