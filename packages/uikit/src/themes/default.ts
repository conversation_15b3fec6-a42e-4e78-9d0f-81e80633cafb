import type { UiThemeDefinition } from '../types'

export default {
  id: 'default',
  name: '默认',
  appearance: 'light',
  translucent: true,
  radius: '0.25rem',
  colors: {
    accent: '#1d2129',
    neutral: '#86909c',
    info: 'iris',
    success: 'grass',
    warning: 'orange',
    error: 'red',
  },
  tokens: {
    background: '#f2f3f5',
    foreground: {
      dimmed: 'ui.neutral.4',
      muted: 'ui.neutral.5',
      default: 'ui.neutral.11',
      contrast: 'ui.neutral.12',
    },
    border: {
      default: 'ui.neutral.4',
      toned: 'ui.neutral.5',
    },
    header: {
      height: '3rem',
    },
    activity: {
      width: '3.5rem',
    },
    sidebar: {
      width: '15rem',
    },
  },
} satisfies UiThemeDefinition
