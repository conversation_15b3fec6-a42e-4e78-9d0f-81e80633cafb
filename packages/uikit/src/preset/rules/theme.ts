import type { Rule } from '../types'
import { isThemeColorName, parseColor, resolveColor } from '../utils'

export const foregroundStyles: Rule[] = [
  [
    /^(?:c|color|text)-(.+)$/,
    ([, c]) => {
      const { color, scale, opacity } = parseColor(c)

      if (isThemeColorName(color)) {
        return {
          color: resolveColor(color, scale, opacity),
        }
      }
    },
    { autocomplete: ['(c|color|text)-<theme-colors>', '(c|color|text)-<theme-colors>-<color-scales>'] },
  ],

  [
    /^(?:c|color|text)-(default|muted|dimmed|contrast)$/,
    ([, color]) => {
      const suffix = color === 'default'
        ? ''
        : `-${color}`

      return { color: `var(--ui-fg${suffix})` }
    },
    { autocomplete: '(c|color|text)-(default|muted|dimmed|contrast)' },
  ],
]

export const backgroundStyles: Rule[] = [
  [
    /^bg-theme$/,
    () => ({ 'background-color': 'var(--ui-bg)' }),
  ],

  [
    /^bg-(.+)$/,
    ([, c]) => {
      const { color, scale, opacity } = parseColor(c)

      if (isThemeColorName(color)) {
        return {
          'background-color': resolveColor(color, scale, opacity),
        }
      }
    },
    { autocomplete: ['bg-<theme-colors>', 'bg-<theme-colors>-<color-scales>'] },
  ],

  // TODO: Gradients (from, via, to, stops)
]

export const borderStyles: Rule[] = [
  [
    /^(?:b|border)-(.+)$/,
    ([, c]) => {
      const { color, scale, opacity } = parseColor(c)

      if (isThemeColorName(color)) {
        return {
          'border-color': resolveColor(color, scale, opacity),
        }
      }
    },
    { autocomplete: '(b|border)-<theme-colors>' },
  ],

  [
    /^(?:b|border)-(default|toned)$/,
    ([, color]) => {
      const suffix = color === 'default'
        ? ''
        : `-${color}`

      return { 'border-color': `var(--ui-border${suffix})` }
    },
    { autocomplete: '(b|border)-(default|toned)' },
  ],
]

export const componentColors: Rule[] = [
  // Foreground
  [
    /^(?:c|color|text)-(activity|brand|panel|tooltip)$/,
    ([, comp]) => ({ color: `var(--ui-${comp}-fg)` }),
    { autocomplete: '(c|color|text)-(activity|brand|panel|tooltip)' },
  ],

  // Background
  [
    /^bg-(activity|brand|header|sidebar|panel|tooltip)$/,
    ([, comp]) => ({ 'background-color': `var(--ui-${comp}-bg)` }),
    { autocomplete: 'bg-(activity|brand|header|sidebar|panel|tooltip)' },
  ],

  // Width
  [
    /^w-(activity|sidebar)$/,
    ([, comp]) => ({ width: `var(--ui-${comp}-width)` }),
    { autocomplete: 'w-(activity|sidebar)' },
  ],

  // Height
  [
    /^h-(header)$/,
    ([, comp]) => ({ height: `var(--ui-${comp}-height)` }),
    { autocomplete: 'h-header' },
  ],
]
