import type { Shortcuts } from '../types'

export const misc = [
  // States
  {
    'is-disabled': 'op-40 select-none pointer-events-none',
  },
  // Utils
  {
    'abs': 'absolute',
    'abs-center': 'left-1/2 -translate-x-1/2 top-1/2 -translate-y-1/2',
    'abs-x-center': 'left-1/2 -translate-x-1/2',
    'abs-y-center': 'top-1/2 -translate-y-1/2',

    'flex-center': 'items-center justify-center',
    'flex-x-center': 'justify-center',
    'flex-y-center': 'items-center',
    'flex-between': 'justify-between',
  },
] satisfies Shortcuts
