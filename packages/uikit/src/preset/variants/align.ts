import type { PresetUikitOptions } from '..'
import type { Variant } from '../types'

const attributes = [
  'start',
  'end',
  'center',
] as const

export function alignVariants(options: PresetUikitOptions): Variant[] {
  const prefix = options.prefix

  return attributes.map(
    attribute => ({
      name: `data-align:${attribute}`,
      match(matcher) {
        if (!matcher.startsWith(`${prefix}${attribute}:`))
          return matcher

        return {
          matcher: matcher.slice(`${prefix}${attribute}:`.length),
          selector: s => `${s}[data-align=${attribute}]`,
        }
      },
      multiPass: true,
    }),
  )
}
