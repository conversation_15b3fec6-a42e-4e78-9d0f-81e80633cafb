import type { PresetUikitOptions } from '..'
import type { Variant } from '../types'

const attributes = [
  'to-start',
  'to-end',
  'from-start',
  'from-end',
] as const

export function motionVariants(options: PresetUikitOptions): Variant[] {
  const prefix = options.prefix

  return attributes.map(
    attribute => ({
      name: `data-motion:${attribute}`,
      match(matcher) {
        if (!matcher.startsWith(`${prefix}${attribute}:`))
          return matcher

        return {
          matcher: matcher.slice(`${prefix}${attribute}:`.length),
          selector: s => `${s}[data-motion=${attribute}]`,
        }
      },
      multiPass: true,
    }),
  )
}
