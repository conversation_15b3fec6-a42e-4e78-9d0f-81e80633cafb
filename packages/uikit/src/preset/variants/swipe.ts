import type { PresetUikitOptions } from '..'
import type { Variant } from '../types'

const attributes = [
  'start',
  'move',
  'cancel',
  'end',
] as const

export function swipeVariants(options: PresetUikitOptions): Variant[] {
  const prefix = options.prefix

  return attributes.map(
    attribute => ({
      name: `data-swipe:${attribute}`,
      match(matcher) {
        if (!matcher.startsWith(`${prefix}${attribute}:`))
          return matcher

        return {
          matcher: matcher.slice(`${prefix}${attribute}:`.length),
          selector: s => `${s}[data-swipe=${attribute}]`,
        }
      },
      multiPass: true,
    }),
  )
}

const directions = [
  'up',
  'down',
  'left',
  'right',
] as const

export function swipeDirectionVariants(options: PresetUikitOptions): Variant[] {
  const prefix = options.prefix

  return directions.map(
    direction => ({
      name: `data-swipe-direction:${direction}`,
      match(matcher) {
        if (!matcher.startsWith(`${prefix}swipe-${direction}:`))
          return matcher

        return {
          matcher: matcher.slice(`${prefix}swipe-${direction}:`.length),
          selector: s => `${s}[data-swipe-direction=${direction}]`,
        }
      },
      multiPass: true,
    }),
  )
}
