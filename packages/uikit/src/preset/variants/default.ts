import type { PresetUikitOptions } from '..'
import type { Variant } from '../types'

import { alignVariants } from './align'
import { motionVariants } from './motion'
import { orientationVariants } from './orientation'
import { presentVariants } from './present'
import { sideVariants } from './side'
import { stateVariants } from './state'
import { swipeDirectionVariants, swipeVariants } from './swipe'

export function variants(options: PresetUikitOptions): Variant[] {
  return [
    ...alignVariants(options),
    ...orientationVariants(options),
    ...presentVariants(options),
    ...sideVariants(options),
    ...stateVariants(options),
    ...motionVariants(options),
    ...swipeVariants(options),
    ...swipeDirectionVariants(options),
  ]
}
