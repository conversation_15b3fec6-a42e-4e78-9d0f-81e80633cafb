import type { PresetUikitOptions } from '..'
import type { Variant } from '../types'

const attributes = [
  'vertical',
  'horizontal',
] as const

export function orientationVariants(options: PresetUikitOptions): Variant[] {
  const prefix = options.prefix

  return attributes.map(
    attribute => ({
      name: `data-orientation:${attribute}`,
      match(matcher) {
        if (!matcher.startsWith(`${prefix}${attribute}:`))
          return matcher

        return {
          matcher: matcher.slice(`${prefix}${attribute}:`.length),
          selector: s => `${s}[data-orientation=${attribute}]`,
        }
      },
      multiPass: true,
    }),
  )
}
