import type { PresetUikitOptions } from '..'
import type { Variant } from '../types'

const attributes = [
  'active',
  'complete',
  'disabled',
  'empty',
  'expanded',
  'focus',
  'focused',
  'highlighted-end',
  'highlighted-start',
  'highlighted',
  'invalid',
  'linear',
  'outside-view',
  'outside-visible-view',
  'placeholder-shown',
  'placeholder',
  'pressed',
  'readonly',
  'selected',
  'selection-end',
  'selection-start',
  'today',
  'unavailable',
] as const

export function presentVariants(options: PresetUikitOptions): Variant[] {
  const prefix = options.prefix

  return attributes.map(
    attribute => ({
      name: `data-${attribute}`,
      match(matcher) {
        if (!matcher.startsWith(`${prefix}${attribute}:`))
          return matcher

        return {
          matcher: matcher.slice(`${prefix}${attribute}:`.length),
          selector: s => `${s}[data-${attribute}]`,
        }
      },
      multiPass: true,
    }),
  )
}
