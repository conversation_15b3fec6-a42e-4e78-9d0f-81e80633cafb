import type { PresetUikitOptions } from '..'
import type { Variant } from '../types'

const attributes = [
  'left',
  'right',
  'top',
  'bottom',
] as const

export function sideVariants(options: PresetUikitOptions): Variant[] {
  const prefix = options.prefix

  return attributes.map(
    attribute => ({
      name: `data-side:${attribute}`,
      match(matcher) {
        if (!matcher.startsWith(`${prefix}${attribute}:`))
          return matcher

        return {
          matcher: matcher.slice(`${prefix}${attribute}:`.length),
          selector: s => `${s}[data-side=${attribute}]`,
        }
      },
      multiPass: true,
    }),
  )
}
