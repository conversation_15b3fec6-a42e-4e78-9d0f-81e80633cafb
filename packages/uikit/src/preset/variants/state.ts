import type { PresetUikitOptions } from '..'
import type { Variant } from '../types'

const attributes = [
  'open',
  'closed',
  'checked',
  'unchecked',
  'indeterminate',
  'complete',
  'indeterminate',
  'loading',
  'on',
  'off',
  'active',
  'inactive',
  'visible',
  'hidden',
  'drag',
  'hover',
  'inactive',
] as const

export function stateVariants(options: PresetUikitOptions): Variant[] {
  const prefix = options.prefix

  return attributes.map(
    attribute => ({
      name: `data-state:${attribute}`,
      match(matcher) {
        if (!matcher.startsWith(`${prefix}${attribute}:`))
          return matcher

        return {
          matcher: matcher.slice(`${prefix}${attribute}:`.length),
          selector: s => `${s}[data-state=${attribute}]`,
        }
      },
      multiPass: true,
    }),
  )
}
