import type { UiTheme, UiThemeColorScales } from '../types'
import type { Theme } from './types'
import { definePreset } from 'unocss'

import { preflights } from './preflights'
import { rules } from './rules'
import { shortcuts } from './shortcuts'
import { theme } from './theme'
import { variants } from './variants'

export interface PresetUikitOptions {
  /**
   * Prefix for Reka Data Attribute variants.
   *
   * `data-[state=open]` short to `ui-open`
   *
   * @default 'ui-'
   */
  prefix?: string
  /**
   * Custom colors
   */
  colors?: Record<string, UiThemeColorScales>
  /**
   * Define themes
   */
  themes?: UiTheme[]
}

export const presetUikit = definePreset<PresetUikitOptions, Theme>((options = {}) => {
  options.prefix = options.prefix ?? 'ui-'

  return {
    name: '@xiaoshop/preset-uikit',
    enforce: 'post',
    extendTheme: (t) => {
      return {
        ...t,
        ...theme(options.colors || {}),
      }
    },
    preflights: preflights(options.themes || []),
    rules,
    shortcuts,
    shorthands: {
      'theme-colors': '(accent|neutral|info|success|warning|error)',
      'color-scales': '(1|2|3|4|5|6|7|8|9|10|11|12)',
    },
    variants: variants(options),
  }
})

export default presetUikit
