import type { UiTheme, UiThemeColorScales } from '../../../src/types'
import type { Preflight } from '../types'
import { toOklchString } from '../../../src/runtime/utils/color'
import { minifyCss } from '../utils'

export function preflightThemes(themes: UiTheme[]): Preflight[] {
  return Object.entries(themes)
    .map(([_, theme]) => ({
      layer: 'base',
      getCSS() {
        const themeColors = makeThemeColors(theme)
        const themeTokens = makeThemeTokens(theme.tokens)

        return minifyCss(`
          .${theme.id} {
            color-scheme:${theme.appearance || 'light'};

            --ui-radius:var(--custom-radius,${theme.radius ? `${theme.radius}` : '0.25rem'});

            ${themeColors}
            ${themeTokens}
          }
        `)
      },
    }))
}

function makeThemeColors(theme: UiTheme) {
  if (theme.colors) {
    return Object.entries(theme.colors)
      .map(([name, color]: [string, UiThemeColorScales | string]) => {
        if (name === 'background') {
          return `--ui-bg:${toOklchString(color as string)};`
        }

        const shaders = color as UiThemeColorScales

        if (name === 'accent' || name === 'neutral') {
          return [
            `--ui-${name}:var(--custom-${name},${shaders[8]});`,
            ...shaders.map(
              (value, shade) => `--ui-${name}-${shade + 1}:var(--custom-${name}-${shade + 1},${value});`,
            ),
          ].join('')
        }

        return [
          `--ui-${name}:${shaders[8]};`,
          ...shaders.map(
            (value, shade) => `--ui-${name}-${shade + 1}:${value};`,
          ),
        ].join('')
      })
      .join('')
  }

  return ''
}

function makeThemeTokens(tokens: UiTheme['tokens']) {
  return Object.entries(tokens)
    .map(([name, tokens]) => {
      if (name === 'foreground') {
        return Object.entries(tokens)
          .map(([key, value]) => key === 'default' ? `--ui-fg:${value};` : `--ui-fg-${key}:${value};`)
          .join('')
      }

      if (name === 'background') {
        return Object.entries(tokens)
          .map(([key, value]) => `--ui-bg-${key}:${value};`)
          .join('')
      }

      return Object.entries(tokens)
        .map(([key, value]) => {
          if (key === 'default')
            return `--ui-${name}:${value};`

          if (key === 'background')
            key = 'bg'

          if (key === 'foreground')
            key = 'fg'

          return `--ui-${name}-${key}:${value};`
        })
        .join('')
    })
    .join('')
}
