import type { Theme } from '../types'
import type { UiThemeColorScales } from '~/src/types'
import { animation } from './animate'
import { colors } from './colors'
import { font, text } from './font'
import { radius } from './misc'
import { breakpoint, verticalBreakpoint } from './size'

export function theme(customColors: Record<string, UiThemeColorScales>): Theme {
  return {
    colors: colors(customColors),
    font,
    text,
    breakpoint,
    verticalBreakpoint,
    radius,
    animation,
  }
}
