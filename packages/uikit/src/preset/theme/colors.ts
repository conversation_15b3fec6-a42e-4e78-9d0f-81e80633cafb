import type { UiThemeColorScales } from '../../types'
import type { Theme } from '../types'
import { transformColor } from '../../runtime/utils/color'

function createDefaultColors() {
  const blackColor = transformColor({ color: 'black' })
  const whiteColor = transformColor({ color: 'white' })

  return {
    black: {
      ...Object.fromEntries(
        blackColor.map((value, i) => [
          i + 1,
          value,
        ]),
      ),
      DEFAULT: blackColor[11],
    },
    white: {
      ...Object.fromEntries(
        whiteColor.map((value, i) => [
          i + 1,
          value,
        ]),
      ),
      DEFAULT: whiteColor[11],
    },
  }
}

function createCustomColors(colors: Record<string, UiThemeColorScales>) {
  return Object.entries(colors).reduce((acc, color) => {
    const [name, scales] = color

    return {
      ...acc,

      [name]: {
        ...Object.fromEntries(
          scales.map((value, i) => [
            i + 1,
            value,
          ]),
        ),
        DEFAULT: scales[8],
      },
    }
  }, {} as Theme['colors'])
}

export function colors(customColors: Record<string, UiThemeColorScales>): Theme['colors'] {
  return {
    ...createDefaultColors(),
    ...createCustomColors(customColors),
  }
}
