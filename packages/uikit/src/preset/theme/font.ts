import type { Theme } from '../types'

export const font = {
  sans: [
    'Inter',
    'system-ui',
    '-apple-system',
    'BlinkMacSystemFont',
    '"PingFang SC"',
    '"Hiragino Sans GB"',
    '"Noto Sans"',
    '"Microsoft YaHei"',
    '"Helvetica Neue"',
    'Helvetica',
    'Arial',
    'sans-serif',
    '"Apple Color Emoji"',
    '"Segoe UI Emoji"',
    '"Segoe UI Symbol"',
    '"Noto Color Emoji"',
  ].join(','),
  mono: [
    'ui-monospace',
    'SFMono-Regular',
    'Menlo',
    'Consolas',
    'monospace',
  ].join(','),
} satisfies Theme['font']

export const text = {
  'xs': { fontSize: '0.75rem', lineHeight: '1rem' },
  'sm': { fontSize: '0.875rem', lineHeight: '1.25rem' },
  'base': { fontSize: '1rem', lineHeight: '1.5rem' },
  'lg': { fontSize: '1.125rem', lineHeight: '1.625rem' },
  'xl': { fontSize: '1.25rem', lineHeight: '1.75rem' },
  '2xl': { fontSize: '1.5rem', lineHeight: '1.875rem' },
  '3xl': { fontSize: '1.75rem', lineHeight: '2.25rem' },
  '4xl': { fontSize: '2.1875rem', lineHeight: '2.5rem' },
  '5xl': { fontSize: '3.75rem', lineHeight: '1' },
} satisfies Theme['text']
