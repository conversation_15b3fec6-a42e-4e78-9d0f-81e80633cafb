export function minifyCss(css: string) {
  return css.trim().replace(/\s+/g, ' ').replace(/\/\*[\s\S]*?\*\//g, '')
}

/**
 * 解析颜色字符串，返回颜色相关信息
 *
 * @example
 * ```ts
 * parseColor('accent') // { color: 'accent' }
 * parseColor('accent/10') // { color: 'accent', opacity: 10 }
 * parseColor('accent-1') // { color: 'accent', scale: 1 }
 * parseColor('accent-1/10') // { color: 'accent', scale: 1, opacity: 10 }
 * parseColor('accent-soft') // { color: 'accent-soft' }
 * parseColor('accent-soft/10') // { color: 'accent-soft', opacity: 10 }
 * parseColor('accent-soft-1') // { color: 'accent-soft', scale: 1 }
 * parseColor('accent-soft-1/10') // { color: 'accent-soft', scale: 1, opacity: 10 }
 * ```
 */
export function parseColor(str: string) {
  const result: {
    color: string
    scale?: number
    opacity?: number
  } = {
    color: str,
  }

  // 处理透明度
  if (str.includes('/')) {
    const [colorPart, opacityStr] = str.split('/')
    const opacity = Number.parseInt(opacityStr, 10)

    if (!Number.isNaN(opacity) && opacity >= 0 && opacity <= 100) {
      result.color = colorPart
      result.opacity = opacity
    }
  }

  // 处理色阶
  const parts = result.color.split('-')
  const lastPart = parts[parts.length - 1]
  const scale = Number.parseInt(lastPart, 10)

  if (!Number.isNaN(scale) && scale >= 1 && scale <= 12) {
    result.scale = scale
    result.color = parts.slice(0, -1).join('-')
  }

  return result
}

export function resolveColor(color: string, scale?: number, opacity?: number) {
  let colorVar = `var(--ui-${color})`

  if (scale !== undefined) {
    colorVar = `var(--ui-${color}-${scale})`
  }

  if (opacity !== undefined) {
    colorVar = `color-mix(in oklch, ${colorVar} ${opacity}%, transparent)`
  }

  return colorVar
}

export function isThemeColorName(name: string): boolean {
  return [
    'accent',
    'neutral',
    'info',
    'success',
    'warning',
    'error',
  ].includes(name)
}
