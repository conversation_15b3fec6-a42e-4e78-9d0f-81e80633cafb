import type { ModuleOptions as IconNuxtOptions } from '@nuxt/icon'
import type { UnocssNuxtOptions } from '@unocss/nuxt'
import type { DEFAULT_ACCENT_COLORS, DEFAULT_NEUTRAL_COLORS } from './runtime/constants'
import type { UiThemeDefinition } from './types'
import { addComponentsDir, addImportsDir, addPlugin, createResolver, defineNuxtModule, hasNuxtModule, installModule } from '@nuxt/kit'
import { defu } from 'defu'
import { name, version } from '../package.json'
import { DEFAULT_ICONS } from './runtime/constants'
import { addTemplates } from './templates'

export type * from './runtime/types'

export type UiKitCustomColor<T> = T | Record<string, string>

export interface UiKitOptions {
  /**
   * UiKit Components Prefix
   *
   * @defaultValue `Ui`
   */
  prefix?: string
  /**
   * Import themes from directories
   */
  imports?: {
    /**
     * Directories to import themes from
     */
    dirs?: string[]
    /**
     * Auto import themes from directories
     */
    auto?: boolean
  }
  /**
   * Inject main css
   */
  css?: boolean
  /**
   * Define accent colors
   *
   * @default ```ts
   * ['tomato', 'red', 'ruby', 'crimson', 'pink', 'plum', 'purple', 'violet', 'iris', 'indigo', 'blue', 'cyan', 'teal', 'jade', 'green', 'grass', 'bronze', 'gold', 'brown', 'orange', 'amber', 'lime', 'mint', 'sky']
   * ```
   */
  accentColors?: UiKitCustomColor<(typeof DEFAULT_ACCENT_COLORS)[number]>[]
  /**
   * Define neutral colors
   *
   * @default ```ts
   * ['gray', 'mauve', 'slate', 'sage', 'olive', 'sand']
   * ```
   */
  neutralColors?: UiKitCustomColor<(typeof DEFAULT_NEUTRAL_COLORS)[number]>[]
  /**
   * Define themes
   */
  themes?: UiThemeDefinition[]
}

export default defineNuxtModule<UiKitOptions>({
  meta: {
    name,
    version,
    configKey: 'uikit',
    compatibility: {
      nuxt: '>=3.17.0',
    },
  },

  defaults: {
    prefix: 'Ui',
    imports: {
      auto: true,
    },
  },

  async setup(options, nuxt) {
    const { resolve } = createResolver(import.meta.url)

    options = options || {}
    options.imports = defu(options.imports || {}, { dirs: [resolve('./themes')] })

    nuxt.options.uikit = options
    nuxt.options.alias['#uikit'] = resolve('./runtime')

    nuxt.options.appConfig.uikit = {
      theme: 'default',
      icons: DEFAULT_ICONS,
    }

    nuxt.options.app.rootAttrs = nuxt.options.app.rootAttrs || {}
    nuxt.options.app.rootAttrs.class = [nuxt.options.app.rootAttrs.class, 'isolate'].filter(Boolean).join(' ')

    if (options.css)
      nuxt.options.css.push(resolve('./runtime/assets/css/main.css'))

    async function registerModule<T extends Record<string, any>>(name: string, key: string, options?: T) {
      if (!hasNuxtModule(name)) {
        await installModule(name, options)
      }
      else {
        (nuxt.options as any)[key] = defu((nuxt.options as any)[key], options || {})
      }
    }

    await registerModule('@vueuse/nuxt', 'vueuse')

    await registerModule<UnocssNuxtOptions>('@unocss/nuxt', 'unocss', {
      uno: false,
      components: false,
      safelist: [
        'isolate',
      ],
    })

    await registerModule<IconNuxtOptions>('@nuxt/icon', 'icon', {
      componentName: 'NuxtIcon',
      cssLayer: 'components',
      customCollections: [
        {
          prefix: 'xiao',
          dir: resolve('./runtime/assets/icon'),
          normalizeIconName: false,
        },
      ],
    })

    addPlugin({ src: resolve('./runtime/plugins/custom-color') })

    addComponentsDir({
      path: resolve('./runtime/components'),
      prefix: options.prefix,
      pathPrefix: false,
      extensions: ['.vue', '.tsx'],
    })

    addImportsDir(resolve('./runtime/composables'))

    await addTemplates(options, resolve)
  },
})
