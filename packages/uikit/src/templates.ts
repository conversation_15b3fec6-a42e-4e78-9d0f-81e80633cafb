import type { Resolver } from '@nuxt/kit'
import type { NuxtTemplate } from 'nuxt/schema'
import type { UiKitOptions, UiTheme } from './module'
import { addTemplate, addTypeTemplate, findPath, useLogger } from '@nuxt/kit'
import { loadThemes, resolveAccentColors, resolveNeutralColors, resolveTheme } from './defaults'
import { DEFAULT_ICONS } from './runtime/constants'

export async function getTemplates(options: UiKitOptions, resolve: Resolver['resolve']): Promise<NuxtTemplate[]> {
  const logger = useLogger()
  const templates: NuxtTemplate[] = []

  const accentColors = resolveAccentColors(options.accentColors)
  const neutralColors = resolveNeutralColors(options.neutralColors)

  const accentLightColors = Object.fromEntries(
    Object.entries(accentColors).filter(([key]) => !key.startsWith('dark')),
  )
  const accentDarkColors = Object.fromEntries(
    Object.entries(accentColors).filter(([key]) => key.startsWith('dark')),
  )

  const neutralLightColors = Object.fromEntries(
    Object.entries(neutralColors).filter(([key]) => !key.startsWith('dark')),
  )
  const neutralDarkColors = Object.fromEntries(
    Object.entries(neutralColors).filter(([key]) => key.startsWith('dark')),
  )

  // #build/uikit/colors.ts
  templates.push({
    filename: 'uikit/colors.ts',
    getContents() {
      logger.info(`UiKit loaded \`${Object.keys(accentLightColors).length}\` accent colors and \`${Object.keys(neutralLightColors).length}\` neutral colors.`)

      return `// Generated by @xiaoshop/uikit
export const ACCENT_COLORS = ${JSON.stringify(accentLightColors, null, 2)} as const

export const ACCENT_COLOR_NAMES = ${JSON.stringify(Object.keys(accentLightColors), null, 2)} as const

export const ACCENT_DARK_COLORS = ${JSON.stringify(accentDarkColors, null, 2)} as const

export const NEUTRAL_COLORS = ${JSON.stringify(neutralLightColors, null, 2)} as const

export const NEUTRAL_COLOR_NAMES = ${JSON.stringify(Object.keys(neutralLightColors), null, 2)} as const

export const NEUTRAL_DARK_COLORS = ${JSON.stringify(neutralDarkColors, null, 2)} as const
`
    },
    write: true,
  })

  const themes: UiTheme[] = [
    ...(options.imports?.auto ? (await loadThemes(options.imports.dirs || [])) : []),
    ...(options.themes ? options.themes.map(theme => resolveTheme(theme)) : []),
  ]

  // #build/uikit/themes.ts
  templates.push({
    filename: 'uikit/themes.ts',
    getContents() {
      logger.info(`UiKit loaded \`${themes.length}\` themes.`)

      return `export const THEMES = ${JSON.stringify(themes, null, 2)} as const`
    },
    write: true,
  })

  // #build/types/uikit-config.d.ts
  templates.push({
    filename: 'types/uikit-config.d.ts',
    getContents() {
      return `// Generated by @xiaoshop/uikit
interface UiAppConfig {
  theme?: UiThemeName
  translucent?: boolean
  radius?: string
  accentColor?: UiAccentColor
  neutralColor?: UiNeutralColor
  icons?: Partial<Record<UiIconName, string>>
}

declare module '@nuxt/schema' {
  interface AppConfigInput {
    uikit?: UiAppConfig
  }

  interface AppConfig {
    uikit: UiAppConfig & Required<Pick<UiAppConfig, 'theme' | 'icons'>>
  }
}

export {}
`
    },
  })

  // #build/types/uikit.d.ts
  templates.push({
    filename: 'types/uikit.d.ts',
    getContents() {
      return `// Generated by @xiaoshop/uikit
declare type UiThemeName = ${themes.map(theme => `'${theme.id}'`).join(' | ')}
declare type UiThemeColor = 'accent' | 'neutral' | 'info' | 'success' | 'warning' | 'error'
declare type UiAccentColor = ${Object.keys(accentLightColors).map(color => `'${color}'`).join(' | ')}
declare type UiNeutralColor = ${Object.keys(neutralLightColors).map(color => `'${color}'`).join(' | ')}
declare type UiIconName = ${Object.keys(DEFAULT_ICONS).map(icon => `'${icon}'`).join(' | ')}
`
    },
  })

  // #build/uno.config.mjs
  templates.push({
    filename: 'uno.config.mjs',
    async getContents() {
      const presetFile = await findPath('index', { cwd: resolve('./preset') })

      return `import {
  defineConfig,
  presetWind4,
  transformerVariantGroup,
} from 'unocss'
import { presetUikit } from '${presetFile}'

export default defineConfig({
  presets: [
    presetWind4({
      reset: true,
      arbitraryVariants: true,
      themePreflight: 'on-demand',
    }),

    presetUikit({
      prefix: 'ui-',
      colors: ${JSON.stringify({ ...accentColors, ...neutralColors }, null, 2)},
      themes: ${JSON.stringify(themes, null, 2)},
    }),
  ],

  transformers: [
    transformerVariantGroup(),
  ],

  content: {
    pipeline: {
      include: [
        /\\.(vue|[jt]sx|ts|mdx?|phtml|html|yml|yaml)($|\\?)/,
        /\\.ui.([jt]s)$/,
      ],
    },
  },
})`
    },
    write: true,
  })

  return templates
}

export async function addTemplates(options: UiKitOptions, resolve: Resolver['resolve']) {
  const templates = await getTemplates(options, resolve)

  templates.forEach((template) => {
    if (template.filename?.endsWith('.d.ts'))
      addTypeTemplate(template as any)
    else
      addTemplate(template)
  })
}
