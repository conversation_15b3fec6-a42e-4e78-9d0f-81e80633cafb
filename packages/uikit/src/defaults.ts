import type { UiKitCustomColor } from './module'
import type { UiTheme, UiThemeColors, UiThemeColorScales, UiThemeDefinition } from './types'
import { DEFAULT_ACCENT_COLORS, DEFAULT_DARK_BG, DEFAULT_LIGHT_BG, DEFAULT_NEUTRAL_COLORS } from './runtime/constants'
import { transformColor, transformToken } from './runtime/utils/color'

export function resolveTheme(theme: UiThemeDefinition): UiTheme {
  const {
    id,
    name,
    appearance = 'light',
    translucent = false,
    radius = '0.25rem',
    colors = {},
    tokens = {},
  } = theme

  const normalized: UiThemeColors = {
    accent: colors.accent ?? 'indigo',
    neutral: colors.neutral ?? 'gray',
    info: colors.info ?? 'blue',
    success: colors.success ?? 'green',
    warning: colors.warning ?? 'orange',
    error: colors.error ?? 'red',
  }

  const background = tokens.background ?? appearance === 'light' ? DEFAULT_LIGHT_BG : DEFAULT_DARK_BG

  return {
    id,
    name,
    appearance,
    translucent,
    radius,

    colors: Object.entries(normalized)
      .reduce((acc, [name, color]: [string, UiThemeColorScales | string]) => ({
        ...acc,
        [name]: resolveThemeColor(
          name as keyof UiThemeColors,
          color,
          appearance,
          background,
        ),
      }), {} as UiThemeColors<UiThemeColorScales>),

    tokens: {
      background: resolveThemeToken(appearance, tokens?.background, background),
      foreground: {
        dimmed: resolveThemeToken(appearance, tokens?.foreground?.dimmed, 'var(--ui-neutral-4)'),
        muted: resolveThemeToken(appearance, tokens?.foreground?.muted, 'var(--ui-neutral-5)'),
        default: resolveThemeToken(appearance, tokens?.foreground?.default, 'var(--ui-neutral-11)'),
        contrast: resolveThemeToken(appearance, tokens?.foreground?.contrast, 'var(--ui-neutral-12)'),
      },
      border: {
        default: resolveThemeToken(appearance, tokens?.border?.default, 'var(--ui-neutral-5)'),
        toned: resolveThemeToken(appearance, tokens?.border?.toned, 'var(--ui-neutral-6)'),
      },
      header: {
        height: resolveThemeToken(appearance, tokens?.header?.height, '3rem'),
        background: resolveThemeToken(appearance, tokens?.header?.background, 'var(--ui-panel-bg)'),
      },
      activity: {
        width: resolveThemeToken(appearance, tokens?.activity?.width, '3.5rem'),
        foreground: resolveThemeToken(appearance, tokens?.activity?.foreground, 'var(--ui-fg)'),
        background: resolveThemeToken(appearance, tokens?.activity?.background, 'var(--ui-panel-bg)'),
      },
      brand: {
        foreground: resolveThemeToken(appearance, tokens?.brand?.foreground, 'var(--ui-accent)'),
        background: resolveThemeToken(appearance, tokens?.brand?.background, 'var(--ui-panel-bg)'),
      },
      sidebar: {
        width: resolveThemeToken(appearance, tokens?.sidebar?.width, '15rem'),
        background: resolveThemeToken(appearance, tokens?.sidebar?.background, 'var(--ui-panel-bg)'),
      },
      panel: {
        foreground: resolveThemeToken(appearance, tokens?.panel?.foreground, 'var(--ui-fg)'),
        background: resolveThemeToken(appearance, tokens?.panel?.background, 'oklch(100% 0 0)'),
      },
      tooltip: {
        background: resolveThemeToken(appearance, tokens?.tooltip?.background, 'var(--ui-neutral-11)'),
        foreground: resolveThemeToken(appearance, tokens?.tooltip?.foreground, 'var(--ui-neutral-1)'),
      },
    },
  }
}

export function resolveThemeColor<
  T extends keyof UiThemeColors,
  RT = T extends 'background' ? string : UiThemeColorScales,
>(
  name: T,
  color: UiThemeColorScales | string,
  appearance: 'light' | 'dark',
  background: string,
): RT {
  return transformColor({
    appearance,
    color: color as any as string,
    isNeutral: name === 'neutral',
    background,
  }) as RT
}

export function resolveThemeToken(
  appearance: 'light' | 'dark',
  value?: string,
  defaultValue?: string,
): string {
  return value
    ? transformToken({ appearance, value })
    : defaultValue || ''
}

export function resolveAccentColors(
  colors?: UiKitCustomColor<(typeof DEFAULT_ACCENT_COLORS)[number]>[],
): Record<string, UiThemeColorScales> {
  const resolvedColors: Record<string, string> = {}

  if (colors) {
    // Color names
    colors.forEach((color) => {
      if (typeof color === 'string') {
        resolvedColors[color] = color
      }
    })
    // Custom colors
    colors.forEach((color) => {
      if (typeof color === 'object' && color !== null) {
        Object.keys(color).forEach((key) => {
          resolvedColors[key] = color[key]
        })
      }
    })
  }
  else {
    DEFAULT_ACCENT_COLORS.forEach((color) => {
      resolvedColors[color] = color
    })
  }

  return Object.entries(resolvedColors).reduce((acc, color) => {
    const [name, value] = color

    acc[name] = transformColor({
      appearance: 'light',
      color: value,
    })

    acc[`dark${name.charAt(0).toUpperCase()}${name.slice(1)}`] = transformColor({
      appearance: 'dark',
      color: value,
    })

    return acc
  }, {} as Record<string, UiThemeColorScales>)
}

export function resolveNeutralColors(
  colors?: UiKitCustomColor<(typeof DEFAULT_NEUTRAL_COLORS)[number]>[],
): Record<string, UiThemeColorScales> {
  const resolvedColors: Record<string, string> = {}

  // Color names
  colors?.forEach((color) => {
    if (typeof color === 'string') {
      resolvedColors[color] = color
    }
  })

  // Default colors
  DEFAULT_NEUTRAL_COLORS.forEach((color) => {
    resolvedColors[color] = color
  })

  // Custom colors
  colors?.forEach((color) => {
    if (typeof color === 'object' && color !== null) {
      Object.keys(color).forEach((key) => {
        resolvedColors[key] = color[key]
      })
    }
  })

  return Object.entries(resolvedColors).reduce((acc, resolved) => {
    const [name, color] = resolved

    acc[name] = transformColor({
      color,
      appearance: 'light',
    })

    acc[`dark${name.charAt(0).toUpperCase()}${name.slice(1)}`] = transformColor({
      color,
      appearance: 'dark',
    })

    return acc
  }, {} as Record<string, UiThemeColorScales>)
}

export async function loadThemes(dirs: string[]): Promise<UiTheme[]> {
  if (!dirs.length)
    return []

  const themes: UiTheme[] = []
  const { default: fg } = await import('fast-glob')

  for (const dir of dirs) {
    const files = await fg([`${dir}/**/*.{ts,js}`])

    for (const file of files) {
      const module = await import(file)
      const theme = module.default || module

      if (theme && typeof theme === 'object')
        themes.push(resolveTheme(theme))
    }
  }

  return themes
}
