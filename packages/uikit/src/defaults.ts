import type { UiKitCustomColor } from './module'
import type { UiTheme, UiThemeColors, UiThemeColorScales, UiThemeDefinition } from './types'
import { DEFAULT_ACCENT_COLORS, DEFAULT_NEUTRAL_COLORS } from './runtime/constants'
import { transformColor, transformToken } from './runtime/utils/color'

export function resolveTheme(theme: UiThemeDefinition): UiTheme {
  const {
    id,
    name,
    appearance = 'light',
    translucent = false,
    radius = '0.25rem',
    colors = {},
    tokens = {},
  } = theme

  const normalized: UiThemeColors = {
    accent: colors.accent ?? 'indigo',
    neutral: colors.neutral ?? 'gray',
    info: colors.info ?? 'blue',
    success: colors.success ?? 'green',
    warning: colors.warning ?? 'orange',
    error: colors.error ?? 'red',
    background: colors.background ?? '#fff',
  }

  return {
    id,
    name,
    appearance,
    translucent,
    radius,

    colors: Object.entries(normalized)
      .reduce((acc, [name, color]: [string, UiThemeColorScales | string]) => ({
        ...acc,
        [name]: resolveThemeColor(
          name as keyof UiThemeColors,
          color,
          appearance,
          normalized.background,
        ),
      }), {} as UiThemeColors<UiThemeColorScales>),

    tokens: {
      foreground: {
        dimmed: resolveThemeToken(appearance, tokens?.foreground?.dimmed, 'var(--ui-neutral-4)'),
        muted: resolveThemeToken(appearance, tokens?.foreground?.muted, 'var(--ui-neutral-5)'),
        toned: resolveThemeToken(appearance, tokens?.foreground?.toned, 'var(--ui-neutral-6)'),
        default: resolveThemeToken(appearance, tokens?.foreground?.default, 'var(--ui-neutral-11)'),
        highlighted: resolveThemeToken(appearance, tokens?.foreground?.highlighted, 'var(--ui-neutral-12)'),
      },
      background: {
        muted: resolveThemeToken(appearance, tokens?.background?.muted, 'var(--ui-accent-3)'),
        elevated: resolveThemeToken(appearance, tokens?.background?.elevated, 'var(--ui-accent-4)'),
        accented: resolveThemeToken(appearance, tokens?.background?.accented, 'var(--ui-accent-5)'),
      },
      border: {
        muted: resolveThemeToken(appearance, tokens?.border?.muted, 'var(--ui-neutral-6)'),
        default: resolveThemeToken(appearance, tokens?.border?.default, 'var(--ui-neutral-7)'),
        accented: resolveThemeToken(appearance, tokens?.border?.accented, 'var(--ui-neutral-8)'),
      },
      header: {
        height: resolveThemeToken(appearance, tokens?.header?.height, '3.5rem'),
        background: resolveThemeToken(appearance, tokens?.header?.background, 'var(--ui-panel-bg)'),
      },
      activity: {
        width: resolveThemeToken(appearance, tokens?.activity?.width, '3rem'),
        background: resolveThemeToken(appearance, tokens?.activity?.background, 'var(--ui-panel-bg)'),
        highlighted: resolveThemeToken(appearance, tokens?.activity?.highlighted, 'var(--ui-accent)'),
      },
      brand: {
        width: resolveThemeToken(appearance, tokens?.brand?.width, 'var(--ui-activity-width)'),
        height: resolveThemeToken(appearance, tokens?.brand?.height, 'var(--ui-header-height)'),
        foreground: resolveThemeToken(appearance, tokens?.brand?.foreground, 'var(--ui-accent)'),
        background: resolveThemeToken(appearance, tokens?.brand?.background, 'var(--ui-panel-bg)'),
      },
      sidebar: {
        width: resolveThemeToken(appearance, tokens?.sidebar?.width, '15rem'),
        background: resolveThemeToken(appearance, tokens?.sidebar?.background, 'var(--ui-panel-bg)'),
      },
      panel: {
        foreground: resolveThemeToken(appearance, tokens?.panel?.foreground, 'var(--ui-fg-default)'),
        background: resolveThemeToken(appearance, tokens?.panel?.background, 'var(--ui-panel-bg)'),
      },
      tooltip: {
        background: resolveThemeToken(appearance, tokens?.tooltip?.background, 'var(--ui-neutral-10)'),
        foreground: resolveThemeToken(appearance, tokens?.tooltip?.foreground, 'var(--ui-neutral-1)'),
      },
    },
  }
}

export function resolveThemeColor<
  T extends keyof UiThemeColors,
  RT = T extends 'background' ? string : UiThemeColorScales,
>(
  name: T,
  color: UiThemeColorScales | string,
  appearance: 'light' | 'dark',
  background: string,
): RT {
  if (name === 'background') {
    return color as RT
  }

  return transformColor({
    appearance,
    color: color as any as string,
    isNeutral: name === 'neutral',
    background,
  }) as RT
}

export function resolveThemeToken(
  appearance: 'light' | 'dark',
  value?: string,
  defaultValue?: string,
): string {
  return value
    ? transformToken({ appearance, value })
    : defaultValue || ''
}

export function resolveAccentColors(
  colors?: UiKitCustomColor<(typeof DEFAULT_ACCENT_COLORS)[number]>[],
): Record<string, UiThemeColorScales> {
  const resolvedColors: Record<string, string> = {}

  if (colors) {
    // Color names
    colors.forEach((color) => {
      if (typeof color === 'string') {
        resolvedColors[color] = color
      }
    })
    // Custom colors
    colors.forEach((color) => {
      if (typeof color === 'object' && color !== null) {
        Object.keys(color).forEach((key) => {
          resolvedColors[key] = color[key]
        })
      }
    })
  }
  else {
    DEFAULT_ACCENT_COLORS.forEach((color) => {
      resolvedColors[color] = color
    })
  }

  return Object.entries(resolvedColors).reduce((acc, color) => {
    Object.keys(color).forEach((name) => {
      acc[name] = transformColor({
        appearance: 'light',
        color: color[name as any],
      })

      acc[`dark${name.charAt(0).toUpperCase()}${name.slice(1)}`] = transformColor({
        appearance: 'dark',
        color: color[name as any],
      })
    })

    return acc
  }, {} as Record<string, UiThemeColorScales>)
}
