export default defineNuxtConfig({
  modules: [
    '../src/module',
  ],

  devtools: {
    enabled: true,
  },

  future: {
    compatibilityVersion: 4,
  },

  features: {
    inlineStyles: false,
  },

  routeRules: {
  },

  uikit: {
    css: true,
  },

  app: {
    head: {
      link: [
        { rel: 'icon', type: 'image/svg+xml', href: '/favicon.svg' },
      ],
    },
  },

  vite: {
    optimizeDeps: {
      include: [
        'reka-ui',
        'reka-ui/namespaced',
        'motion-v',
        'tailwind-variants',
        'clsx',
        'tailwind-merge',
        'scule',
        'ohash/utils',
      ],
    },
  },

  postcss: {
    plugins: {
      'postcss-nested': {},
    },
  },

  compatibilityDate: '2025-05-08',
})
