<script lang="ts" setup>
</script>

<template>
  <header class="sticky top-0 flex-(~ y-center between) px-6 h-header bg-header b-(b default)">
    <div class="flex-(~ y-center) gap-2 c-contrast select-none">
      <UiIcon name="xiao:logo" class="size-5" />
      <h1 class="text-(base) font-bold">
        UiKit
      </h1>
    </div>
  </header>

  <main class="px-6 py-4">
    <slot />
  </main>
</template>
