{"name": "@xiaoshop/uikit", "type": "module", "version": "1.0.0", "private": true, "packageManager": "pnpm@10.12.4", "description": "XiaoShop 云链小店 (@uikit)", "author": "<PERSON> <<EMAIL>>", "license": "Apache-2.0", "homepage": "https://github.com/moujinet/xiaoshop#readme", "repository": {"type": "git", "url": "git+https://github.com/moujinet/xiaoshop.git", "directory": "packages/uikit"}, "bugs": {"url": "https://github.com/moujinet/xiaoshop/issues"}, "exports": {".": {"types": "./dist/types.d.mts", "import": "./dist/module.mjs"}, "./runtime/*": "./dist/runtime/*", "./components/*": "./dist/runtime/components/*", "./composables/*": "./dist/runtime/composables/*", "./utils": {"types": "./dist/runtime/utils/index.d.ts", "import": "./dist/runtime/utils/index.js"}, "./utils/*": {"types": "./dist/runtime/utils/*.d.ts", "import": "./dist/runtime/utils/*.js"}}, "main": "./dist/module.mjs", "typesVersions": {"*": {".": ["./dist/types.d.mts"], "runtime/*": ["./dist/runtime/*"], "components/*": ["./dist/runtime/components/*"], "composables/*": ["./dist/runtime/composables/*"], "utils": ["./dist/runtime/utils/index.d.ts"], "utils/*": ["./dist/runtime/utils/*.d.ts"]}}, "files": ["dist"], "scripts": {"build": "nuxt-module-build build", "prepack": "pnpm build", "dev": "nuxi dev playground", "dev:build": "nuxi build playground", "dev:prepare": "nuxt-module-build build --stub && nuxt-module-build prepare && nuxi prepare playground", "lint": "eslint .", "test": "vitest run", "typecheck": "vue-tsc --noEmit && cd playground && vue-tsc --noEmit"}, "dependencies": {"@nuxt/kit": "catalog:", "clsx": "catalog:", "defu": "catalog:", "motion-v": "catalog:", "reka-ui": "catalog:", "tailwind-merge": "catalog:", "tailwind-variants": "catalog:"}, "devDependencies": {"@nuxt/devtools": "catalog:", "@nuxt/icon": "catalog:", "@nuxt/module-builder": "catalog:", "@nuxt/schema": "catalog:", "@nuxt/test-utils": "catalog:", "@nuxtjs/color-mode": "catalog:", "@radix-ui/colors": "catalog:", "@types/node": "catalog:", "@unocss/nuxt": "catalog:", "@vueuse/core": "catalog:", "@vueuse/nuxt": "catalog:", "@xiaoshop/shared": "workspace:*", "bezier-easing": "catalog:", "citty": "catalog:", "colorjs.io": "catalog:", "consola": "catalog:", "eslint": "catalog:", "fast-glob": "catalog:", "handlebars": "catalog:", "mkdirp": "catalog:", "nuxt": "catalog:", "pathe": "catalog:", "scule": "catalog:", "typescript": "catalog:", "unocss": "catalog:", "vitest": "catalog:", "vue": "catalog:", "vue-tsc": "catalog:"}}