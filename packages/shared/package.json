{"name": "@xiaoshop/shared", "type": "module", "version": "1.0.0", "packageManager": "pnpm@10.12.4", "description": "XiaoShop 云链小店 (@shared)", "author": "<PERSON> <<EMAIL>>", "license": "Apache-2.0", "homepage": "https://github.com/moujinet/xiaoshop#readme", "repository": {"type": "git", "url": "git+https://github.com/moujinet/xiaoshop.git", "directory": "packages/shared"}, "bugs": {"url": "https://github.com/moujinet/xiaoshop/issues"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./types": {"types": "./dist/types/index.d.ts"}}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "files": ["*.d.ts", "dist"], "scripts": {"build": "pnpm clean && unbuild", "clean": "rimraf ./dist", "prepack": "pnpm build"}, "dependencies": {"ohash": "catalog:"}, "devDependencies": {"rimraf": "catalog:", "unbuild": "catalog:"}}