import { isEqual } from 'ohash/utils'

export function getType<T>(value: T): string {
  return Object.prototype.toString.call(value).slice(8, -1)
}

export function isString(value: unknown): value is string {
  return getType(value) === 'String'
}

export function isNumber(value: unknown): value is number {
  return getType(value) === 'Number'
}

export function isBoolean(value: unknown): value is boolean {
  return getType(value) === 'Boolean'
}

export function isObject(value: unknown): value is object {
  return getType(value) === 'Object'
}

export function isArray(value: unknown): value is unknown[] {
  return getType(value) === 'Array'
}

export function isFunction(value: unknown): value is (...args: any[]) => any {
  return getType(value) === 'Function'
}

export function isNull(value: unknown): value is null {
  return getType(value) === 'Null'
}

export function isUndefined(value: unknown): value is undefined {
  return getType(value) === 'Undefined'
}

export function isNullish(value: unknown): value is null | undefined {
  return isNull(value) || isUndefined(value)
}

export function isSymbol(value: unknown): value is symbol {
  return getType(value) === 'Symbol'
}

export function isDate(value: unknown): value is Date {
  return getType(value) === 'Date'
}

export function isRegExp(value: unknown): value is RegExp {
  return getType(value) === 'RegExp'
}

export function isMap(value: unknown): value is Map<unknown, unknown> {
  return getType(value) === 'Map'
}

export function isSet(value: unknown): value is Set<unknown> {
  return getType(value) === 'Set'
}

export function isPromise(value: unknown): value is Promise<unknown> {
  return getType(value) === 'Promise'
}

export function isPrimitive(value: unknown): value is string | number | boolean | symbol | null | undefined | bigint {
  const type = getType(value)
  return ['String', 'Number', 'Boolean', 'Symbol', 'Null', 'Undefined', 'BigInt'].includes(type)
}

export function isPlainObject(value: unknown): value is Record<string, unknown> {
  if (getType(value) !== 'Object')
    return false
  const prototype = Object.getPrototypeOf(value)
  return prototype === null || prototype === Object.prototype
}

export function isArrayOfArray<T>(item: T[] | T[][]): item is T[][] {
  return Array.isArray(item) && Array.isArray(item[0])
}

export function isValueEqualOrExist<T>(base: T | T[] | undefined, current: T | T[] | undefined) {
  if (isNullish(base))
    return false
  if (Array.isArray(base)) {
    return base.some(val => isEqual(val, current))
  }
  else {
    return isEqual(base, current)
  }
}
