export function pick<D extends object, K extends keyof D>(
  data: D,
  keys: K[],
): Pick<D, K> {
  const result = {} as Pick<D, K>

  for (const key of keys) {
    result[key] = data[key]
  }

  return result
}

export function omit<D extends object, K extends keyof D>(
  data: D,
  keys: K[],
): Omit<D, K> {
  const result = { ...data }

  for (const key of keys) {
    delete result[key]
  }

  return result as Omit<D, K>
}
