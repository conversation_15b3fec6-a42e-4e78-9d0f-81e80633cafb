import { defineCollection, defineContentConfig, z } from '@nuxt/content'

export default defineContentConfig({
  collections: {
    content: defineCollection({
      type: 'page',
      source: '**/*',
      schema: z.object({
        label: z.string().optional(),
        icon: z.string().optional(),
        navigation: z.object({
          title: z.string().optional(),
          description: z.string().optional(),
        })
          .optional(),
        badge: z.object({
          label: z.string().optional(),
          color: z.string().optional(),
        })
          .optional(),
        tags: z.array(
          z.object({
            label: z.string(),
            icon: z.string().optional(),
          }),
        )
          .optional(),
      }),
    }),
  },
})
