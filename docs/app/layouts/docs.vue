<script lang="ts" setup>
import type { ContentNavigationItem } from '@nuxt/content'

const navigation = inject<Ref<ContentNavigationItem[]>>('navigation')
</script>

<template>
  <div class="relative h-vh">
    <Header />

    <main class="relative flex h-[calc(100vh-3.5rem)] overflow-auto bg-panel">
      <Sidebar :navigation="navigation" />

      <div class="flex-1">
        <div class="flex-(~ y-center between) h-12 px-6 bg-panel b-(b border)">
          <Breadcrumb :navigation="navigation" />
        </div>

        <UiScrollbar class="h-[calc(100%-3rem)]">
          <div class="py-12">
            <slot />
          </div>
        </UiScrollbar>
      </div>
    </main>
  </div>
</template>
