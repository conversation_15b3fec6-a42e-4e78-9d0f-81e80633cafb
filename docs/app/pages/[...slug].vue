<script lang="ts" setup>
const route = useRoute()

const { data: page } = await useAsyncData(route.path, () => {
  return queryCollection('content').path(route.path).first()
})

if (!page.value) {
  throw createError({
    statusCode: 404,
    statusMessage: 'Page not found',
    fatal: true,
  })
}

const pageTitle = page.value?.navigation?.title ? page.value?.navigation?.title : page.value?.title
const pageDescription = page.value?.navigation?.description ? page.value?.navigation?.description : page.value?.description

useSeoMeta({
  title: pageTitle,
  description: pageDescription,
})

provide('page', page)
</script>

<template>
  <div class="flex size-full">
    <section class="flex-1 mx-auto max-w-[80ch]">
      <ContentRenderer v-if="page?.body" :value="page" />
    </section>

    <div v-if="page?.body.toc?.links?.length" class="relative w-80 px-8">
      <div class="fixed right-8 w-62">
        <!-- <Toc :toc="page?.body.toc" /> -->
      </div>
    </div>
  </div>
</template>
