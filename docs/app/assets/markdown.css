.markdown :where(p) {
  margin-bottom: 1.25rem;
  color: var(--ui-text);
}

.markdown :where(h1, h2, h3, h4, h5, h6) {
  color: var(--ui-text-highlighted);
  font-weight: 600;
}

.markdown :where(h1, h2, h3, h4) {
  margin: 1.5rem 0 0.5rem;
  line-height: 1.25;
}

.markdown :where(h1) {
  margin-top: 0;
  font-size: 2rem;
  line-height: 1.2;
}

.markdown :where(h2) {
  font-size: 1.75rem;
  line-height: 1.3;
}

.markdown :where(h3) {
  font-size: 1.5rem;
  line-height: 1.4;
}

.markdown :where(h4) {
  font-size: 1.25rem;
  line-height: 1.5;
}

.markdown :where(h5) {
  font-size: 1.125rem;
  line-height: 1.5;
}

.markdown :where(h6) {
  font-size: 1rem;
  line-height: 1.5;
}

.markdown :where(small) {
  font-size: 0.75rem;
  line-height: 1rem;
}

.markdown :where(img) {
  border-radius: 50%;
  height: 12.5rem;
  width: 12.5rem;
  margin: 0 auto;
  display: block;
}

.markdown :where(a, a:visited) {
  color: var(--ui-text-highlighted);
  text-decoration: none;
}

.markdown :where(a:hover, a:focus, a:active) {
  color: var(--ui-primary);
}

.markdown :where(pre) {
  background-color: #f9fafb;
  padding: 1rem;
  text-align: left;
  border-radius: 0.375rem;
  overflow-x: auto;
}

.markdown :where(blockquote) {
  margin: 0;
  border-left: 5px solid #6b7280;
  font-style: italic;
  padding: 1.25rem;
  text-align: left;
  background-color: #f9fafb;
}

.markdown :where(ul, ol, li) {
  text-align: left;
}

.markdown :where(code) {
  background-color: #f3f4f6;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
}

.markdown :where(table) {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
}

.markdown :where(th, td) {
  border: 1px solid #d1d5db;
  padding: 0.5rem;
  text-align: left;
}

.markdown :where(th) {
  background-color: #f9fafb;
  font-weight: 600;
}
