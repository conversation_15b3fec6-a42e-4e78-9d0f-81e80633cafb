<script lang="ts" setup>
defineProps<{
  title?: string
  icon?: string
}>()
</script>

<template>
  <a
    v-bind="$attrs"
    class="flex gap-x-4 rounded-lg px-6 py-4.5 no-underline b-(~ border) hover:(b-accent bg-accent-2) transition-colors"
  >
    <UiIcon v-if="icon" :name="icon" class="c-accent size-6" />
    <div class="flex-(~ col)">
      <strong class="c-highlighted text-lg font-medium">
        {{ title }}
      </strong>
      <div class="text-[15px] c-toned *:first:mt-0 *:last:mb-0 *:my-1 mt-1">
        <slot />
      </div>
    </div>
  </a>
</template>
