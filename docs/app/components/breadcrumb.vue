<script lang="ts" setup>
import type { BreadcrumbItem } from '#ui/types'
import type { ContentNavigationItem } from '@nuxt/content'

const props = defineProps<{
  navigation?: ContentNavigationItem[]
}>()

const route = useRoute()

const breadcrumbs = computed<BreadcrumbItem[]>(() => {
  if (!props.navigation || !route.path)
    return []

  // 递归查找匹配当前路径的导航项
  function findPathInNavigation(items: ContentNavigationItem[], currentPath: string, breadcrumbPath: BreadcrumbItem[] = []): BreadcrumbItem[] | null {
    for (const item of items) {
      // 确保 path 和 title 存在且为字符串类型
      const itemPath = typeof item.path === 'string' ? item.path : ''
      const itemTitle = typeof item.title === 'string' ? item.title : (itemPath ? itemPath.split('/').pop() || '' : '')

      // 跳过没有路径的项
      if (!itemPath)
        continue

      // 构建当前项的面包屑路径
      const currentBreadcrumb: BreadcrumbItem = {
        title: itemTitle,
        to: itemPath,
      }

      const newPath = [...breadcrumbPath, currentBreadcrumb]

      // 如果当前项的路径匹配目标路径
      if (itemPath === currentPath) {
        return newPath
      }

      // 如果当前路径是目标路径的前缀，继续在子项中查找
      if (currentPath.startsWith(itemPath) && item.children) {
        const found = findPathInNavigation(item.children, currentPath, newPath)
        if (found) {
          return found
        }
      }
    }

    return null
  }

  // 查找匹配的路径
  const foundPath = findPathInNavigation(props.navigation, route.path)

  // 移除最后一项（当前页面），只保留父级路径
  return foundPath ? foundPath.slice(0, -1) : []
})
</script>

<template>
  <UiBreadcrumb v-if="breadcrumbs.length > 0" :items="breadcrumbs" class="text-sm" />
</template>
