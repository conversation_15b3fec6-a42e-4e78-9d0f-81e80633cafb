<script lang="ts" setup>
import { cn } from '#uikit/utils'

const navs = useNavs()
</script>

<template>
  <NavigationMenuRoot as="header" class="sticky top-0 h-14 b-(y muted) z-10">
    <div class="flex-(~ y-center between) gap-x-2 size-full px-6 bg-panel">
      <div class="flex-(~ 1 y-center)">
        <Logo class="w-54" />

        <NavigationMenuList class="flex-(~ y-center) h-14">
          <NavigationMenuItem v-for="nav in navs" :key="nav.title">
            <NavigationMenuTrigger
              class="group flex-(inline y-center) gap-1.5 py-3 px-3.5 text-sm c-highlighted font-medium transition-color data-[state=open]:c-highlighted"
            >
              {{ nav.title }}
              <UiIcon name="down" class="c-muted group-data-[state=open]:c-text" />
            </NavigationMenuTrigger>

            <NavigationMenuContent
              :class="cn(
                'abs top-0 left-0 w-full will-change-transform',
                'data-[motion=from-start]:animate-(slide-in-left duration-450 ease-in-out)',
                'data-[motion=from-end]:animate-(slide-in-right duration-450 ease-in-out)',
                'data-[motion=to-start]:animate-(slide-out-left duration-350 ease-in-out)',
                'data-[motion=to-end]:animate-(slide-out-right duration-350 ease-in-out)',
              )"
            >
              <NavigationMenuList class="px-60 py-4 grid-(~ cols-4) gap-3">
                <NavigationMenuItem v-for="child in nav.children" :key="child.title">
                  <NavigationMenuLink :href="child.to" class="group relative flex-(~ y-center) gap-x-3 p-2 rounded-md transition-colors hover:bg-muted">
                    <div
                      v-if="child.icon"
                      class="flex-(~ center) size-10.5 rounded-md b-(~ muted) group-hover:(b-accented bg-panel)"
                    >
                      <UiIcon :name="child.icon" class="size-4.5 group-hover:c-highlighted" />
                    </div>

                    <div class="flex-(~ col)">
                      <strong class="text-sm font-medium group-hover:c-highlighted">
                        {{ child.title }}
                      </strong>
                      <p class="text-xs c-toned">
                        {{ child.description }}
                      </p>
                    </div>
                  </NavigationMenuLink>
                </NavigationMenuItem>
              </NavigationMenuList>
            </NavigationMenuContent>
          </NavigationMenuItem>
        </NavigationMenuList>
      </div>

      <div class="flex-(~ y-center) justify-self-end gap-8">
        <!-- <UiSpace>
          <UiLink icon="ri:brush-line" color="secondary" class="flex text-5" />
          <UiLink icon="ri:github-line" color="secondary" href="https://github.com/xiaoshop" class="flex text-5" />
        </UiSpace>

        <UiInputSearch placeholder="Search..." leading="search" /> -->
      </div>
    </div>

    <div class="abs w-full top-14 left-0 -z-10">
      <NavigationMenuViewport
        :class="cn(
          'relative overflow-hidden bg-panel rounded-b-lg b-(b muted)',
          'h-$reka-navigation-menu-viewport-height w-full transition-height',
          'data-[state=open]:animate-(fade-in-down duration-200 ease-in-out)',
          'data-[state=closed]:animate-(fade-out-up duration-150 ease-in-out)',
        )"
      />
    </div>
  </NavigationMenuRoot>
</template>
