<script lang="ts" setup>
import type { PageCollectionItemBase } from '@nuxt/content'

const props = defineProps<{
  toc?: PageCollectionItemBase['body']['toc']
}>()

const links = computed(() => {
  return props.toc?.links.filter(link => link.depth === 2)
})
</script>

<template>
  <div class="flex-(~ col) gap-y-1">
    <div class="flex-(~ y-center) gap-x-2 text-sm c-text-muted">
      <UiIcon name="ri:align-left" />
      大纲
    </div>
    <ul class="flex-(~ col) gap-1.5 b-(l border) text-sm ml-1.5 py-2">
      <template v-for="link in links" :key="link.id">
        <li class="pl-4 -ms-0.25">
          <UiLink color="secondary" :href="`#${link.id}`">
            {{ link.text }}
          </UiLink>
        </li>
      </template>
    </ul>
  </div>
</template>
