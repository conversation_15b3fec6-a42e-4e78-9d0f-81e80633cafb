interface NavItem {
  title: string
  description?: string
  icon?: string
  to?: string
  badge?: {
    label: string
    color: string
  }
  children?: NavItem[]
}

export function useNavs(): NavItem[] {
  return [
    {
      title: '文档',
      children: [
        { title: '开发文档', description: '如何开发 XiaoShop', to: '/docs/guide', icon: 'ri:braces-line' },
        { title: '用户手册', description: '系统功能介绍及使用说明', to: '/docs/manual', icon: 'ri:book-open-line' },
        { title: 'API 文档', description: '接口文档及数据结构', to: '/docs/api', icon: 'ri:code-s-slash-line' },
        { title: '示例', description: '扩展方法及示例代码', to: '/docs/examples', icon: 'ri:box-3-line' },
      ],
    },
    {
      title: '生态',
      children: [
        { title: '应用市场', description: '功能模块、插件及主题模板', to: '/ecosystem/marketplace', icon: 'ri:function-line' },
        { title: '社区支持', description: '社区讨论及官微群组', to: '/ecosystem/community', icon: 'ri:question-answer-line' },
        { title: '视频教程', description: '在线课程及视频教程', to: '/ecosystem/courses', icon: 'ri:video-line' },
        { title: '赞助商', description: '为我们提供帮助的所有人', to: '/ecosystem/sponsors', icon: 'ri:trophy-line' },
      ],
    },
    {
      title: '产品',
      children: [
        { title: 'XiaoShop', description: '云链小店', to: '/products/shop', icon: 'ri:store-2-line' },
        { title: 'XiaoStudio', description: '云链工坊', to: '/products/studio', icon: 'ri:home-office-line' },
        { title: 'XiaoConnect', description: '云链互联协议', to: '/products/connect', icon: 'ri:rfid-line' },
        { title: 'UiKit', description: '中后台组件库', to: '/products/uikit', icon: 'ri:color-filter-line' },
      ],
    },
    {
      title: '关于',
      children: [
        { title: '常见问题', description: '常见问题及解答', to: '/about/faq', icon: 'ri:question-line' },
        { title: '团队成员', description: '云链团队成员介绍', to: '/about/team', icon: 'ri:open-arm-line' },
        { title: '合作伙伴', description: '合作伙伴介绍', to: '/about/partners', icon: 'ri:shake-hands-line' },
        { title: '发行日志', description: '版本更新日志', to: '/about/releases', icon: 'ri:send-plane-line' },
      ],
    },
  ]
}
