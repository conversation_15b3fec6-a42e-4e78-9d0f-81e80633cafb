{"name": "@xiaoshop/docs", "type": "module", "version": "1.0.0", "private": true, "packageManager": "pnpm@10.12.4", "description": "XiaoShop 云链小店 (@docs)", "author": "<PERSON> <<EMAIL>>", "license": "Apache-2.0", "homepage": "https://github.com/moujinet/xiaoshop#readme", "repository": {"type": "git", "url": "git+https://github.com/moujinet/xiaoshop.git", "directory": "docs"}, "bugs": {"url": "https://github.com/moujinet/xiaoshop/issues"}, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxt/content": "catalog:", "nuxt": "catalog:", "reka-ui": "catalog:", "vue": "catalog:", "vue-router": "catalog:"}, "devDependencies": {"@nuxt/kit": "catalog:", "vite-plugin-devtools-json": "catalog:"}}