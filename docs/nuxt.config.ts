import ViteDevtool<PERSON>son from 'vite-plugin-devtools-json'

export default defineNuxtConfig({
  modules: [
    '@nuxt/content',
    'reka-ui/nuxt',
    '../packages/uikit/src/module',
  ],

  compatibilityDate: '2024-11-01',

  future: {
    compatibilityVersion: 4,
  },

  devtools: {
    enabled: true,
  },

  css: [
    '~/assets/markdown.css',
  ],

  uikit: {
    css: true,
  },

  app: {
    head: {
      link: [
        { rel: 'icon', type: 'image/svg+xml', href: '/favicon.svg' },
      ],
    },
  },

  vite: {
    plugins: [
      ViteDevtoolJson(),
    ],

    optimizeDeps: {
      include: [
        'reka-ui',
        'reka-ui/namespaced',
        'motion-v',
        'tailwind-variants',
        'clsx',
        'tailwind-merge',
        'scule',
        'ohash/utils',
        '@radix-ui/colors',
        'bezier-easing',
        'colorjs.io',
      ],
    },
  },
})
