---
title: UiKit
description: UiKit 是 XiaoShop 云链小店的中后台组件库
---

# UiKit

UiKit 是 XiaoShop 云链小店的中后台组件库，基于 [Nuxt](https://nuxt.com)、[UnoCSS](https://unocss.dev) 开发，提供了一套完整的中后台组件库，帮助你快速构建中后台应用。

------

## 快速开始

::card-group{class="grid-cols-1"}
::card{title="概述" icon="ri:rocket-2-line" href="/products/uikit/overview/introduction"}
介绍项目背景、设计理念和使用场景。
::

::card{title="开发指南" icon="ri:book-open-line" href="/products/uikit/guide/getting-start"}
手把手的开发指南，从开发环境到安装使用，助你快速上手 UiKit。
::

::card{title="组件" icon="ri:function-line" href="/products/uikit/components"}
面向不同需求场景提供，基础组件、业务组件和图表组件。
::

::card{title="工具" icon="ri:compasses-2-line" href="/products/uikit/utilities/config-provider"}
Composables、Hooks、Utils 等工具。
::
::
