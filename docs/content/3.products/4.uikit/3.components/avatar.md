# Avatar 头像组件

Avatar 组件用于显示用户头像，支持图片、文字回退和多种样式变体。基于 Reka UI 的 Avatar 组件构建，提供了丰富的自定义选项。

## 基础用法

```vue
<template>
  <!-- 使用图片 -->
  <UiAvatar
    src="https://avatars.githubusercontent.com/u/16436160?s=64&v=4"
    alt="用户头像"
    fallback="A"
  />

  <!-- 使用文字回退 -->
  <UiAvatar fallback="张三" />

  <!-- 使用默认插槽 -->
  <UiAvatar>
    <UiIcon name="user" />
  </UiAvatar>
</template>
```

## 颜色变体

Avatar 组件支持多种颜色主题，使用 soft 变体提供柔和的背景色：

```vue
<template>
  <UiSpace>
    <UiAvatar color="primary" fallback="P" />
    <UiAvatar color="secondary" fallback="S" />
    <UiAvatar color="info" fallback="I" />
    <UiAvatar color="success" fallback="S" />
    <UiAvatar color="warning" fallback="W" />
    <UiAvatar color="error" fallback="E" />
    <UiAvatar color="neutral" fallback="N" />
  </UiSpace>
</template>
```

## 尺寸规格

提供 8 种不同的尺寸选择，从 xs 到 4xl：

```vue
<template>
  <UiSpace align="center">
    <UiAvatar size="xs" fallback="XS" />
    <UiAvatar size="sm" fallback="SM" />
    <UiAvatar size="md" fallback="MD" />
    <UiAvatar size="lg" fallback="LG" />
    <UiAvatar size="xl" fallback="XL" />
    <UiAvatar size="2xl" fallback="2XL" />
    <UiAvatar size="3xl" fallback="3XL" />
    <UiAvatar size="4xl" fallback="4XL" />
  </UiSpace>
</template>
```

## 圆角样式

支持不同的圆角样式，从无圆角到完全圆形：

```vue
<template>
  <UiSpace>
    <UiAvatar radius="none" fallback="无" />
    <UiAvatar radius="sm" fallback="小" />
    <UiAvatar radius="md" fallback="中" />
    <UiAvatar radius="lg" fallback="大" />
    <UiAvatar radius="full" fallback="圆" />
  </UiSpace>
</template>
```

## 头像组合

使用 `UiAvatarGroup` 组件可以将多个头像组合显示，创建重叠效果：

```vue
<template>
  <UiAvatarGroup color="success" size="lg">
    <UiAvatar src="https://avatars.githubusercontent.com/u/16436160?s=64&v=4" fallback="A" />
    <UiAvatar fallback="B" />
    <UiAvatar fallback="C" />
    <UiAvatar fallback="D" />
    <UiAvatar fallback="E" />
  </UiAvatarGroup>
</template>
```

## 加载状态

Avatar 组件支持图片加载失败时的回退机制，可以设置延迟时间：

```vue
<template>
  <!-- 快速显示回退内容 -->
  <UiAvatar
    src="invalid-url.jpg"
    fallback="失败"
    :delay="100"
  />

  <!-- 延迟显示回退内容 -->
  <UiAvatar
    src="slow-loading.jpg"
    fallback="加载中"
    :delay="1000"
  />
</template>
```

## Props 属性

### AvatarProps

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `src` | `string` | - | 头像图片地址 |
| `alt` | `string` | - | 图片的替代文本，用于无障碍访问 |
| `fallback` | `string` | - | 图片加载失败时显示的文字 |
| `delay` | `number` | `400` | 回退内容显示延迟时间（毫秒） |
| `color` | `AvatarColor` | `'secondary'` | 颜色主题 |
| `size` | `AvatarSize` | `'md'` | 尺寸大小 |
| `radius` | `AvatarRadius` | `'full'` | 圆角样式 |
| `class` | `string` | - | 自定义 CSS 类名 |
| `ui` | `AvatarStyleSlots` | - | 自定义样式插槽 |

### 类型定义

```typescript
// 颜色选项
type AvatarColor = 'primary' | 'secondary' | 'info' | 'success' | 'warning' | 'error' | 'neutral'

// 尺寸选项
type AvatarSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl'

// 圆角选项
type AvatarRadius = 'none' | 'sm' | 'md' | 'lg' | 'full'

// 样式插槽
interface AvatarStyleSlots {
  root?: string      // 根容器样式
  image?: string     // 图片样式
  fallback?: string  // 回退内容样式
}
```

## Emits 事件

Avatar 组件本身不发出任何自定义事件，但会透传原生 DOM 事件。

## 插槽

### 默认插槽

用于自定义头像内容，当没有提供 `src` 和 `fallback` 时显示：

```vue
<template>
  <!-- 使用图标 -->
  <UiAvatar>
    <UiIcon name="user" size="16" />
  </UiAvatar>

  <!-- 使用自定义内容 -->
  <UiAvatar>
    <div class="text-xs font-bold">VIP</div>
  </UiAvatar>
</template>
```

## 样式定制

### 使用 ui 属性

通过 `ui` 属性可以自定义各个部分的样式：

```vue
<template>
  <UiAvatar
    :ui="{
      root: 'border-2 border-primary shadow-lg',
      fallback: 'text-primary font-bold text-lg'
    }"
    fallback="自定义"
  />
</template>
```

### 使用 CSS 类

```vue
<template>
  <UiAvatar
    class="shadow-lg hover:scale-110 transition-transform duration-200"
    fallback="悬停"
  />
</template>
```

### 自定义颜色

```vue
<template>
  <UiAvatar
    :ui="{
      root: 'bg-gradient-to-br from-purple-400 to-pink-400 text-white'
    }"
    fallback="渐变"
  />
</template>
```

## 尺寸对照表

| 尺寸 | 像素大小 | 文字大小 | 使用场景 |
|------|----------|----------|----------|
| `xs` | 20px | 10px | 小型标签、徽章 |
| `sm` | 24px | 12px | 列表项、评论 |
| `md` | 32px | 12px | 默认尺寸、表单 |
| `lg` | 40px | 14px | 卡片、导航 |
| `xl` | 64px | 24px | 个人资料 |
| `2xl` | 80px | 40px | 大型展示 |
| `3xl` | 96px | 48px | 头像编辑 |
| `4xl` | 128px | 64px | 特大展示 |

## 无障碍访问

Avatar 组件遵循无障碍访问标准：

```vue
<template>
  <!-- 提供有意义的 alt 文本 -->
  <UiAvatar
    src="user-avatar.jpg"
    alt="张三的头像"
    fallback="张三"
  />

  <!-- 对于装饰性头像，可以使用空 alt -->
  <UiAvatar
    src="decorative-avatar.jpg"
    alt=""
    fallback="装饰"
  />
</template>
```

---

# AvatarGroup 头像组合组件

AvatarGroup 组件用于将多个 Avatar 组件组合显示，通常用于显示团队成员、参与者列表等场景。

## 基础用法

```vue
<template>
  <UiAvatarGroup>
    <UiAvatar src="https://avatars.githubusercontent.com/u/1?s=64&v=4" fallback="A" />
    <UiAvatar src="https://avatars.githubusercontent.com/u/2?s=64&v=4" fallback="B" />
    <UiAvatar fallback="C" />
    <UiAvatar fallback="D" />
  </UiAvatarGroup>
</template>
```

## 统一样式

AvatarGroup 可以为所有子 Avatar 统一设置样式属性：

```vue
<template>
  <!-- 统一颜色和尺寸 -->
  <UiAvatarGroup color="primary" size="lg" radius="md">
    <UiAvatar fallback="A" />
    <UiAvatar fallback="B" />
    <UiAvatar fallback="C" />
  </UiAvatarGroup>
</template>
```

## AvatarGroup Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `as` | `string` | `'span'` | 渲染的 HTML 标签 |
| `asChild` | `boolean` | `false` | 是否作为子组件渲染 |
| `color` | `AvatarColor` | `'secondary'` | 统一的颜色主题 |
| `size` | `AvatarSize` | `'md'` | 统一的尺寸大小 |
| `radius` | `AvatarRadius` | `'full'` | 统一的圆角样式 |
| `delay` | `number` | `100` | 回退内容显示延迟时间 |
| `class` | `string` | - | 自定义 CSS 类名 |
| `ui` | `AvatarGroupStyleSlots` | - | 自定义样式插槽 |

### AvatarGroup 样式插槽

```typescript
interface AvatarGroupStyleSlots {
  root?: string      // 容器样式
  children?: string  // 子元素样式
}
```

## 重叠间距

不同尺寸的头像组合会自动调整重叠间距：

```vue
<template>
  <!-- 小尺寸，较小间距 -->
  <UiAvatarGroup size="sm">
    <UiAvatar fallback="A" />
    <UiAvatar fallback="B" />
    <UiAvatar fallback="C" />
  </UiAvatarGroup>

  <!-- 大尺寸，较大间距 -->
  <UiAvatarGroup size="xl">
    <UiAvatar fallback="A" />
    <UiAvatar fallback="B" />
    <UiAvatar fallback="C" />
  </UiAvatarGroup>
</template>
```

## 最佳实践

### 1. 图片优化

```vue
<template>
  <!-- ✅ 推荐：使用适当尺寸的图片 -->
  <UiAvatar
    src="https://example.com/avatar-64x64.jpg"
    size="md"
    fallback="用户"
  />

  <!-- ❌ 避免：使用过大的图片 -->
  <UiAvatar
    src="https://example.com/avatar-1024x1024.jpg"
    size="sm"
    fallback="用户"
  />
</template>
```

### 2. 回退文字

```vue
<template>
  <!-- ✅ 推荐：使用有意义的回退文字 -->
  <UiAvatar
    src="user-avatar.jpg"
    fallback="张三"
    alt="张三的头像"
  />

  <!-- ✅ 推荐：使用用户名首字母 -->
  <UiAvatar
    src="user-avatar.jpg"
    :fallback="user.name.charAt(0)"
    :alt="`${user.name}的头像`"
  />
</template>
```

### 3. 一致性设计

```vue
<template>
  <!-- ✅ 推荐：在同一界面保持一致的尺寸 -->
  <div class="user-list">
    <UiAvatar v-for="user in users" :key="user.id" size="md" :src="user.avatar" :fallback="user.name" />
  </div>

  <!-- ✅ 推荐：根据上下文选择合适的尺寸 -->
  <div class="profile-header">
    <UiAvatar size="xl" :src="currentUser.avatar" :fallback="currentUser.name" />
  </div>
</template>
```

### 4. 加载状态处理

```vue
<template>
  <!-- ✅ 推荐：为慢速网络设置合适的延迟 -->
  <UiAvatar
    :src="user.avatar"
    :fallback="user.name"
    :delay="600"
  />
</template>
```

## 常见用例

### 用户资料

```vue
<template>
  <div class="profile-card">
    <UiAvatar
      size="xl"
      :src="user.avatar"
      :fallback="user.name.charAt(0)"
      :alt="`${user.name}的头像`"
    />
    <h3>{{ user.name }}</h3>
  </div>
</template>
```

### 团队展示

```vue
<template>
  <div class="team-section">
    <h4>团队成员</h4>
    <UiAvatarGroup size="lg" color="primary">
      <UiAvatar
        v-for="member in team"
        :key="member.id"
        :src="member.avatar"
        :fallback="member.name.charAt(0)"
        :alt="`${member.name}的头像`"
      />
    </UiAvatarGroup>
  </div>
</template>
```

### 评论列表

```vue
<template>
  <div class="comment-list">
    <div v-for="comment in comments" :key="comment.id" class="comment-item">
      <UiAvatar
        size="sm"
        :src="comment.author.avatar"
        :fallback="comment.author.name.charAt(0)"
      />
      <div class="comment-content">
        <strong>{{ comment.author.name }}</strong>
        <p>{{ comment.content }}</p>
      </div>
    </div>
  </div>
</template>
```

## 相关组件

- [Icon](./icon.md) - 图标组件，可用于头像插槽
- [Image](./image.md) - 图片组件，了解图片处理
- [Space](./space.md) - 间距组件，用于头像布局
- [Badge](./badge.md) - 徽章组件，可与头像组合使用