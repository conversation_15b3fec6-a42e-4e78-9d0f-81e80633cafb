packages:
  - apps/*
  - packages/*
  - services/*
  - docs

catalog:
  '@antfu/eslint-config': ^4.16.1
  '@commitlint/cli': ^19.8.1
  '@commitlint/config-conventional': ^19.8.1
  '@iconify-json/mingcute': ^1.2.3
  '@iconify-json/ri': ^1.2.5
  '@nuxt/content': ^3.6.1
  '@nuxt/devtools': ^2.5.0
  '@nuxt/icon': 1.13.0
  '@nuxt/image': ^1.10.0
  '@nuxt/kit': ^3.17.5
  '@nuxt/module-builder': ^1.0.1
  '@nuxt/schema': ^3.17.5
  '@nuxt/test-utils': ^3.19.1
  '@nuxtjs/color-mode': 3.5.2
  '@radix-ui/colors': ^3.0.0
  '@types/node': latest
  '@unocss/nuxt': 66.1.0-beta.12
  '@vueuse/core': 13.3.0
  '@vueuse/nuxt': 13.3.0
  bezier-easing: ^2.1.0
  citty: ^0.1.6
  clsx: ^2.1.1
  colorjs.io: ^0.5.2
  commitizen: ^4.3.1
  consola: ^3.4.2
  cross-env: ^7.0.3
  cz-git: ^1.11.2
  czg: ^1.11.2
  defu: ^6.1.4
  eslint: ^9.29.0
  eslint-plugin-pnpm: ^0.3.1
  fast-glob: ^3.3.3
  handlebars: ^4.7.8
  jsonc-eslint-parser: ^2.4.0
  lint-staged: ^16.1.2
  mkdirp: ^3.0.1
  motion-v: ^1.3.1
  nuxt: ^3.17.5
  ohash: ^2.0.11
  pathe: ^2.0.3
  postcss: ^8.5.6
  postcss-nested: ^7.0.2
  reka-ui: ^2.3.1
  rimraf: ^6.0.1
  scule: ^1.3.0
  simple-git-hooks: ^2.13.0
  std-env: ^3.9.0
  tailwind-merge: ^3.3.1
  tailwind-variants: ^1.0.0
  typescript: ^5.8.3
  unbuild: ^3.5.0
  unocss: 66.1.0-beta.12
  unocss-preset-animations: ^1.2.1
  vite: ^7.0.0
  vite-plugin-devtools-json: ^0.2.0
  vitest: ^3.2.4
  vue: ^3.5.17
  vue-router: ^4.5.1
  vue-tsc: ^2.2.10
  yaml-eslint-parser: ^1.3.0

onlyBuiltDependencies:
  - '@parcel/watcher'
  - esbuild
  - sharp
  - simple-git-hooks
  - unrs-resolver
  - vue-demi
  - better-sqlite3

hoist: true
engineStrict: true
autoInstallPeers: true
shamefullyHoist: true
